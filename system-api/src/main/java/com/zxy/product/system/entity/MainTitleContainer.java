package com.zxy.product.system.entity;

/**
* @Description:    主标题容器
* @Author:         liuc
* @CreateDate:     2021/6/23 20:03
* @UpdateRemark:   修改内容
* @Version:        1.0
*/
public class MainTitleContainer {
    /**
     * 显示开关
     */
    private int switchStatus;
    /**
     * 文字区间最小值
     */
    private String minFontNumber;
    /**
     * 文字区间最大值
     */
    private String maxFontNumber;
    /**
     * 字体
     */
    private String fontFamily;
    /**
     * 字号
     */
    private String fontSize;
    /**
     * 颜色
     */
    private String color;
    /**
     * 加粗 bold/normal
     */
    private String fontWeight;
    /**
     * 斜体 italic/normal
     */
    private String fontStyle;
    /**
     * 下划线 underline/none
     */
    private String textDecoration;
    /**
     * 文字对齐 center/left/right
     */
    private String textAlign;
    /**
     * 行间距
     */
    private String lineHeight;
    /**
     * 宽度
     */
    private String width;
    /**
     * x坐标
     */
    private String left;
    /**
     * y坐标
     */
    private String top;


    public int getSwitchStatus() {
        return switchStatus;
    }

    public void setSwitchStatus(int switchStatus) {
        this.switchStatus = switchStatus;
    }

    public String getMinFontNumber() {
        return minFontNumber;
    }

    public int getTitleMinLength() {
        return Integer.valueOf(minFontNumber);
    }

    public int getTitleMaxLength() {
        return Integer.valueOf(maxFontNumber);
    }

    private static String getNumberFromStr(String val){
        return val.substring(0,val.indexOf("px"));
    }

    public void setMinFontNumber(String minFontNumber) {
        this.minFontNumber = minFontNumber;
    }

    public String getMaxFontNumber() {
        return maxFontNumber;
    }

    public void setMaxFontNumber(String maxFontNumber) {
        this.maxFontNumber = maxFontNumber;
    }

    public String getFontFamily() {
        return fontFamily;
    }

    public void setFontFamily(String fontFamily) {
        this.fontFamily = fontFamily;
    }

    public String getFontSize() {
        return fontSize;
    }

    public void setFontSize(String fontSize) {
        this.fontSize = fontSize;
    }

    public String getColor() {
        return color;
    }

    public void setColor(String color) {
        this.color = color;
    }

    public String getFontWeight() {
        return fontWeight;
    }

    public void setFontWeight(String fontWeight) {
        this.fontWeight = fontWeight;
    }

    public String getFontStyle() {
        return fontStyle;
    }

    public void setFontStyle(String fontStyle) {
        this.fontStyle = fontStyle;
    }

    public String getTextDecoration() {
        return textDecoration;
    }

    public void setTextDecoration(String textDecoration) {
        this.textDecoration = textDecoration;
    }

    public String getTextAlign() {
        return textAlign;
    }

    public void setTextAlign(String textAlign) {
        this.textAlign = textAlign;
    }

    public String getLineHeight() {
        return lineHeight;
    }

    public void setLineHeight(String lineHeight) {
        this.lineHeight = lineHeight;
    }

    public String getWidth() {
        return width;
    }

    public void setWidth(String width) {
        this.width = width;
    }

    public String getLeft() {
        return left;
    }

    public void setLeft(String left) {
        this.left = left;
    }

    public String getTop() {
        return top;
    }

    public void setTop(String top) {
        this.top = top;
    }
}
