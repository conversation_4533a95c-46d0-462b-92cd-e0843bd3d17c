package com.zxy.product.system.entity;

import com.zxy.product.system.domain.vo.HomeParentVO;
import com.zxy.product.system.jooq.tables.pojos.HomeContentEntity;

import java.io.Serializable;

/**
 * Created by cheng<PERSON> on 17/2/22.
 */
public class HomeContent extends HomeContentEntity {

    private static final long serialVersionUID = -5766895005515579451L;

    public final static Integer DATA_TYPE_IMAGE = 11;

    public final static Integer DATA_TYPE_INTERACTIVE_LEARNING = 12;

    /**
     * 好书推荐类型
     */
    public final static Integer DATA_TYPE_BOOK_RECOMMENDATION = 14;

    private String image;

    private String imagePath;

    private String organizationName;
    
    private String appImage;
    
    private String appImagePath;
    
    private HomeContentImage contentImage;
    
    private HomeContentImage appContentImage;

    /** 自定义模块能展示的最大数量 */
    public static Integer FETCH_MAX_LIMIT = 250;

    public String getAppImage() {
		return appImage;
	}

	public void setAppImage(String appImage) {
		this.appImage = appImage;
	}

	public String getAppImagePath() {
		return appImagePath;
	}

	public void setAppImagePath(String appImagePath) {
		this.appImagePath = appImagePath;
	}

	public HomeContentImage getContentImage() {
		return contentImage;
	}

	public void setContentImage(HomeContentImage contentImage) {
		this.contentImage = contentImage;
	}

	public HomeContentImage getAppContentImage() {
		return appContentImage;
	}

	public void setAppContentImage(HomeContentImage appContentImage) {
		this.appContentImage = appContentImage;
	}

	public String getImagePath() {
        return imagePath;
    }

    public void setImagePath(String imagePath) {
        this.imagePath = imagePath;
    }

    public String getImage() {
        return image;
    }

    public void setImage(String image) {
        this.image = image;
    }

    public String getOrganizationName() {
        return organizationName;
    }

    public void setOrganizationName(String organizationName) {
        this.organizationName = organizationName;
    }




    private String coverPath;
    public void setCoverPath(String coverPath){ this.coverPath=coverPath; }
    public String getCoverPath(){ return coverPath; }


    /**
     * 首页配置块：互动学习子VO
     * <AUTHOR>
     * @date 2024年08月02日 9:22
     */
    public static class InteractiveContentChild extends HomeParentVO implements Serializable{
        private static final long serialVersionUID = 6570371893109909280L;

        /**首页配置块：互动学习数据扩展字段（可用于丰富描述等）*/
        private String dataExt;

        /**首页配置块：互动学习URL连接地址*/
        private String url;

        /**首页配置块：互动学习数据名称*/
        private String dataName;

        public String getDataExt() { return dataExt; }

        public void setDataExt(String dataExt) { this.dataExt = dataExt; }

        public String getUrl() { return url; }

        public void setUrl(String url) { this.url = url; }

        public String getDataName() { return dataName; }

        public void setDataName(String dataName) { this.dataName = dataName; }

        @Override
        public String toString() {
            return "ContentChild{" +
                    "dataExt='" + dataExt + '\'' +
                    ", url='" + url + '\'' +
                    ", dataName='" + dataName + '\'' +
                    '}';
        }
    }

    /**
     * 首页配置块：精选内容子VO
     * <AUTHOR>
     * @date 2024年08月02日 9:22
     */
    public static class ContentChild extends HomeParentVO implements Serializable{
        private static final long serialVersionUID = -6845596642735527773L;

        /**首页配置块：精选内容Id*/
        private String id;

        /**首页配置块：精选内容数据Id*/
        private String dataId;

        /**首页配置块：精选内容数据名称*/
        private String dataName;

        /**首页配置块：精选内容数据类型 9课程 10专题*/
        private Integer dataType;

        /**首页配置块：精选内容数据简介*/
        private String dataExt;

        public String getId() { return id; }

        public void setId(String id) { this.id = id; }

        public String getDataId() { return dataId; }

        public void setDataId(String dataId) { this.dataId = dataId; }

        public String getDataName() { return dataName; }

        public void setDataName(String dataName) { this.dataName = dataName; }

        public Integer getDataType() { return dataType; }

        public void setDataType(Integer dataType) { this.dataType = dataType; }

        public String getDataExt() { return dataExt; }

        public void setDataExt(String dataExt) { this.dataExt = dataExt; }

        @Override
        public String toString() {
            return "FeaturedContentChild{" +
                    "id='" + id + '\'' +
                    ", dataId='" + dataId + '\'' +
                    ", dataName='" + dataName + '\'' +
                    ", dataType=" + dataType +
                    '}';
        }
    }

    /**
     * 首页配置块：自定义内容子VO
     * <AUTHOR>
     * @date 2024年08月02日 9:22
     */
    public static class CustomizeContentChild extends  HomeParentVO implements Serializable{
        private static final long serialVersionUID = 4317546775651527913L;

        /**首页配置块：自定义内容Id*/
        private String id;

        /**首页配置块：自定义内容数据Id*/
        private String dataId;

        /**首页配置块：自定义内容数据名称*/
        private String dataName;

        /**首页配置块：自定义内容数据类型*/
        private Integer dataType;

        /**首页配置块：自定义内容URL链接地址*/
        private String url;

        /**首页配置块：自定义内容简介*/
        private String dataExt;

        public String getId() { return id; }

        public void setId(String id) { this.id = id; }

        public String getDataId() { return dataId; }

        public void setDataId(String dataId) { this.dataId = dataId; }

        public String getDataName() { return dataName; }

        public void setDataName(String dataName) { this.dataName = dataName; }

        public Integer getDataType() { return dataType; }

        public void setDataType(Integer dataType) { this.dataType = dataType; }

        public String getUrl() { return url; }

        public void setUrl(String url) { this.url = url; }

        public String getDataExt() { return dataExt; }

        public void setDataExt(String dataExt) { this.dataExt = dataExt; }

        @Override
        public String toString() {
            return "CustomizeContentChild{" +
                    "id='" + id + '\'' +
                    ", dataId='" + dataId + '\'' +
                    ", dataName='" + dataName + '\'' +
                    ", dataType=" + dataType +
                    ", url='" + url + '\'' +
                    ", dataExt='" + dataExt + '\'' +
                    '}';
        }
    }
}