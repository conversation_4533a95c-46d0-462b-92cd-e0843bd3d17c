package com.zxy.product.system.domain.vo.home.Interactive;

import java.io.Serializable;

/**
 * 首页互动学习：覆写PC回显VO
 * <AUTHOR>
 * @date 2024年11月25日 11:27
 */
public class InteractivePcVO extends InteractiveParentVO implements Serializable {
    private static final long serialVersionUID = 3897714118426258315L;

    /**互动学习：数据简介*/
    private String dataExt;

    /**互动学习：数据名称*/
    private String dataName;

    /**互动学习：跳转URL地址*/
    private String url;

    public String getDataExt() { return dataExt; }

    public void setDataExt(String dataExt) { this.dataExt = dataExt; }

    public String getDataName() { return dataName; }

    public void setDataName(String dataName) { this.dataName = dataName; }

    public String getUrl() { return url; }

    public void setUrl(String url) { this.url = url; }

    @Override
    public String toString() {
        return "InteractivePcVO{" +
                "dataExt='" + dataExt + '\'' +
                ", dataName='" + dataName + '\'' +
                ", url='" + url + '\'' +
                '}';
    }
}
