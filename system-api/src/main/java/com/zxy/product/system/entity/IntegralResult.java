package com.zxy.product.system.entity;

import java.math.BigDecimal;

import com.zxy.product.system.jooq.tables.pojos.IntegralResultEntity;

/**
 * 用户积分结果表
 * <AUTHOR>
 *
 */
public class IntegralResult extends IntegralResultEntity{

    private static final long serialVersionUID = -1500783464062007565L;
    
    public static final Integer DEFAULT_RANK = 0; // 无积分则排名为0

    private String memberName;

    private String organizationName;

    private IntegralGrade integralGrade;//积分等级

    private String headPortrait;
    
    private BigDecimal thisYearScore;//今年积分

    public BigDecimal getThisYearScore() {
		return thisYearScore;
	}

	public void setThisYearScore(BigDecimal thisYearScore) {
		this.thisYearScore = thisYearScore;
	}

	public String getHeadPortrait() {
        return headPortrait;
    }

    public void setHeadPortrait(String headPortrait) {
        this.headPortrait = headPortrait;
    }

    public String getMemberName() {
        return memberName;
    }

    public void setMemberName(String memberName) {
        this.memberName = memberName;
    }

    public String getOrganizationName() {
        return organizationName;
    }

    public void setOrganizationName(String organizationName) {
        this.organizationName = organizationName;
    }

    public IntegralGrade getIntegralGrade() {
        return integralGrade;
    }

    public void setIntegralGrade(IntegralGrade integralGrade) {
        this.integralGrade = integralGrade;
    }

}
