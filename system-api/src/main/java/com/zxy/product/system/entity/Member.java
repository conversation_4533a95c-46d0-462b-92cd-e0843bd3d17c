package com.zxy.product.system.entity;

import com.zxy.product.system.jooq.tables.pojos.MemberEntity;

import java.util.List;

public class Member extends MemberEntity {

    private static final long serialVersionUID = -8686007683984030632L;

    /**
     * 禁用
     */
    public static final int STATUS_DISABLE = 0;
	private Integer index; // 导出序号
    private Organization organization; // 所属部门
    private Organization companyOrganization; // 所属机构
    private Organization rootOrganization; // 所属根组织
    private Integer initSetting;
    private boolean self;
    private List<Role> roles;

    private String roleId;
    private String roleName;
    private Integer roleOrder;
    private Long roleCreateTime;
    private String roleOrgId;
    private String roleOrgName;
    private String grantOrgId;
    private String grantOrgName;
    private Integer menuLevel;
    private String menuName;
    private Integer grantOrgChildFind;
    private Integer from;
    private String grantId;

    public Long getRoleCreateTime() {
        return roleCreateTime;
    }

    public void setRoleCreateTime(Long roleCreateTime) {
        this.roleCreateTime = roleCreateTime;
    }

    public Integer getRoleOrder() {
        return roleOrder;
    }

    public void setRoleOrder(Integer roleOrder) {
        this.roleOrder = roleOrder;
    }

    public String getGrantId() {
        return grantId;
    }

    public void setGrantId(String grantId) {
        this.grantId = grantId;
    }

    public Integer getMenuLevel() {
        return menuLevel;
    }

    public void setMenuLevel(Integer menuLevel) {
        this.menuLevel = menuLevel;
    }

    public String getMenuName() {
        return menuName;
    }

    public void setMenuName(String menuName) {
        this.menuName = menuName;
    }

    public String getGrantOrgId() {
        return grantOrgId;
    }

    public void setGrantOrgId(String grantOrgId) {
        this.grantOrgId = grantOrgId;
    }

    public String getGrantOrgName() {
        return grantOrgName;
    }

    public void setGrantOrgName(String grantOrgName) {
        this.grantOrgName = grantOrgName;
    }

    public String getRoleId() {
        return roleId;
    }

    public void setRoleId(String roleId) {
        this.roleId = roleId;
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }

    public String getRoleOrgId() {
        return roleOrgId;
    }

    public void setRoleOrgId(String roleOrgId) {
        this.roleOrgId = roleOrgId;
    }

    public String getRoleOrgName() {
        return roleOrgName;
    }

    public void setRoleOrgName(String roleOrgName) {
        this.roleOrgName = roleOrgName;
    }

    public List<Role> getRoles() {
        return roles;
    }

    public void setRoles(List<Role> roles) {
        this.roles = roles;
    }

    public Integer getIndex() {
		return index;
	}

	public void setIndex(Integer index) {
		this.index = index;
	}

	public Organization getOrganization() {
        return organization;
    }

    public void setOrganization(Organization organization) {
        this.organization = organization;
    }

    public Organization getCompanyOrganization() {
        return companyOrganization;
    }

    public void setCompanyOrganization(Organization companyOrganization) {
        this.companyOrganization = companyOrganization;
    }

    public Organization getRootOrganization() {
        return rootOrganization;
    }

    public void setRootOrganization(Organization rootOrganization) {
        this.rootOrganization = rootOrganization;
    }

    public Integer getInitSetting() {
        return initSetting;
    }

    public void setInitSetting(Integer initSetting) {
        this.initSetting = initSetting;
    }

	public boolean getSelf() {
		return self;
	}

	public void setSelf(boolean self) {
		this.self = self;
	}

    public Integer getGrantOrgChildFind() {
        return grantOrgChildFind;
    }

    public void setGrantOrgChildFind(Integer grantOrgChildFind) {
        this.grantOrgChildFind = grantOrgChildFind;
    }

    public Integer getFrom() {
        return from;
    }

    public void setFrom(Integer from) {
        this.from = from;
    }
}
