package com.zxy.product.system.domain.vo.home.category.course;

import java.io.Serializable;
import java.util.List;

/**
 * 首页分类课程：PC回显VO
 * <AUTHOR>
 * @date 2024年11月25日 15:50
 */
public class CategoryCoursePcVO extends CategoryCourseParentVO implements Serializable {
    private static final long serialVersionUID = 7296587528681818791L;

    /**分类课程：配置Id*/
    private String id;

    /**分类课程：数据Id*/
    private String dataId;

    /**分类课程：数据名称*/
    private String dataName;

    /**分类课程：图片地址*/
    private String imgPath;

    /**分类课程：子集数据*/
    private List<CategoryCoursePcVO> children;

    public String getId() { return id; }

    public void setId(String id) { this.id = id; }

    public String getDataId() { return dataId; }

    public void setDataId(String dataId) { this.dataId = dataId; }

    public String getDataName() { return dataName; }

    public void setDataName(String dataName) { this.dataName = dataName; }

    public String getImgPath() { return imgPath; }

    public void setImgPath(String imgPath) { this.imgPath = imgPath; }

    public List<CategoryCoursePcVO> getChildren() { return children; }

    public void setChildren(List<CategoryCoursePcVO> children) { this.children = children; }

    @Override
    public String toString() {
        return "CategoryCoursePcVO{" +
                "id='" + id + '\'' +
                ", dataId='" + dataId + '\'' +
                ", dataName='" + dataName + '\'' +
                ", imgPath='" + imgPath + '\'' +
                ", children=" + children +
                '}';
    }
}
