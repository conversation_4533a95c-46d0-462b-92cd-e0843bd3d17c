package com.zxy.product.system.result;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class ScreenshotGuardConfig implements Serializable {
    private static final long serialVersionUID = -3570659413176439215L;

    boolean appscreen = false;// App防录屏黑屏效果
    String[] influence = {};// 课程、考试、知识是否选中，选中的在数组中显示
    //不同类型的文件配置
    Video video;// 视频
    FileBookImag fileBookImag;// 文档&电子书&图片
    ExamPaper examPaper;// 试卷

    public boolean isAppscreen() {
        return appscreen;
    }

    public void setAppscreen(boolean appscreen) {
        this.appscreen = appscreen;
    }

    public String[] getInfluence() {
        return influence;
    }

    public void setInfluence(String[] influence) {
        this.influence = influence;
    }

    public Video getVideo() {
        return video;
    }

    public void setVideo(Video video) {
        this.video = video;
    }

    public FileBookImag getFileBookImag() {
        return fileBookImag;
    }

    public void setFileBookImag(FileBookImag fileBookImag) {
        this.fileBookImag = fileBookImag;
    }

    public ExamPaper getExamPaper() {
        return examPaper;
    }

    public void setExamPaper(ExamPaper examPaper) {
        this.examPaper = examPaper;
    }
}
