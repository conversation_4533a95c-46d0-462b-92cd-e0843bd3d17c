package com.zxy.product.system.domain.vo.home.category.course;

import java.io.Serializable;
import java.util.List;

/**
 * 首页分类课程：App回显VO
 * <AUTHOR>
 * @date 2024年11月25日 15:51
 */
public class CategoryCourseAppVO extends CategoryCourseParentVO implements Serializable {
    private static final long serialVersionUID = 8876865649542064109L;

    /**分类课程：配置Id*/
    private String id;

    /**分类课程：数据名称*/
    private String dataName;

    /**分类课程：数据图片地址*/
    private String imgPath;

    /**分类课程：数据Id*/
    private String dataId;

    /**分类课程：数据类型*/
    private Integer dataType;

    /**分类课程：URL地址*/
    private String url;

    /**分类课程：子集数据*/
    private List<CategoryCourseAppVO> children;

    public String getId() { return id; }

    public void setId(String id) { this.id = id; }

    public String getDataName() { return dataName; }

    public void setDataName(String dataName) { this.dataName = dataName; }

    public String getImgPath() { return imgPath; }

    public void setImgPath(String imgPath) { this.imgPath = imgPath; }

    public String getDataId() { return dataId; }

    public void setDataId(String dataId) { this.dataId = dataId; }

    public Integer getDataType() { return dataType; }

    public void setDataType(Integer dataType) { this.dataType = dataType; }

    public String getUrl() { return url; }

    public void setUrl(String url) { this.url = url; }

    public List<CategoryCourseAppVO> getChildren() { return children; }

    public void setChildren(List<CategoryCourseAppVO> children) { this.children = children; }

    @Override
    public String toString() {
        return "CategoryCourseAppVO{" +
                "id='" + id + '\'' +
                ", dataName='" + dataName + '\'' +
                ", imgPath='" + imgPath + '\'' +
                ", dataId='" + dataId + '\'' +
                ", dataType=" + dataType +
                ", url='" + url + '\'' +
                '}';
    }
}
