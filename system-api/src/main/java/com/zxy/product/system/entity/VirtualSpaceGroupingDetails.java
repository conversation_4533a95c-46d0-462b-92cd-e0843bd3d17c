package com.zxy.product.system.entity;

import com.zxy.product.system.jooq.tables.pojos.VirtualSpaceGroupingDetailsEntity;

/**
 * <AUTHOR>
 */
public class VirtualSpaceGroupingDetails extends VirtualSpaceGroupingDetailsEntity {

    private String HomeConfigId;
    private String HomeConfigName;

    private String OrgName;
    private String organizationId;

    private String logo;
    private String logoPath;

    public String getOrganizationId() {
        return organizationId;
    }

    public void setOrganizationId(String organizationId) {
        this.organizationId = organizationId;
    }

    public String getHomeConfigName() {
        return HomeConfigName;
    }

    public void setHomeConfigName(String homeConfigName) {
        HomeConfigName = homeConfigName;
    }

    public String getOrgName() {
        return OrgName;
    }

    public void setOrgName(String orgName) {
        OrgName = orgName;
    }

    public String getLogo() {
        return logo;
    }

    public void setLogo(String logo) {
        this.logo = logo;
    }

    public String getLogoPath() {
        return logoPath;
    }

    public void setLogoPath(String logoPath) {
        this.logoPath = logoPath;
    }
}
