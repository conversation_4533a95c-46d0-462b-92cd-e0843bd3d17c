package com.zxy.product.system.entity;

import com.zxy.product.system.jooq.tables.pojos.PointDailyQuestEntity;

import java.io.Serializable;
import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @create 2023/12/4 17:33
 */
public class PointDailyQuest extends PointDailyQuestEntity implements Serializable {

    //每日任务总次数,存在一次加多次分
    public static Set<String> dailyInfoByConditionSetTotal = new HashSet<String>(){
        {
            add("course_required_hour");
            add("watch_live_hour");
            add("watch_short_video_minute");
            add("cloud_study_course");
        }
    };

    //每日任务每次触发一次,一次最多加一次分
    public static Set<String> dailyInfoByConditionSetOnce = new HashSet<String>(){
        {

            add("complete_exam");
            add("publish_ask_bar");
            add("upload_expert");
            add("publish_short_video");
            add("share");
            add("selected_comments");
            add("studio_experts_discuss");
            add("report");
        }
    };
}
