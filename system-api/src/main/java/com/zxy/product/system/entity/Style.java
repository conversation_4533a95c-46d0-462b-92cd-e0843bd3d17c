package com.zxy.product.system.entity;


import com.zxy.product.system.jooq.tables.pojos.StyleEntity;

/**
* @Description:    样式实体类
* @Author:         liuc
* @CreateDate:     2021/6/23 17:31
* @UpdateRemark:   修改内容
* @Version:        1.0
*/
public class Style extends StyleEntity {
    private static final long serialVersionUID = -4721210494981415252L;

    private String createrCode;

    private String creater;

    private String organizationName;

    private String scenarios;
    private String size;

    private Integer canvesType;

    public Integer getCanvesType() {
        return canvesType;
    }

    public void setCanvesType(Integer canvesType) {
        this.canvesType = canvesType;
    }

    public String getCreater() {
        return creater;
    }

    public void setCreater(String creater) {
        this.creater = creater;
    }

    public String getSize() {
        return size;
    }

    public void setSize(String size) {
        this.size = size;
    }

    public String getScenarios() {
        return scenarios;
    }

    public void setScenarios(String scenarios) {
        this.scenarios = scenarios;
    }

    public String getCreaterCode() {
        return createrCode;
    }

    public void setCreaterCode(String createrCode) {
        this.createrCode = createrCode;
    }

    public String getOrganizationName() {
        return organizationName;
    }

    public void setOrganizationName(String organizationName) {
        this.organizationName = organizationName;
    }
}
