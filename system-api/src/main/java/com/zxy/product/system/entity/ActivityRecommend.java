package com.zxy.product.system.entity;


import com.zxy.product.system.jooq.tables.pojos.ActivityRecommendEntity;

/**
 * 活动推荐表
 */
public class ActivityRecommend extends ActivityRecommendEntity {
	private static final long serialVersionUID = -3560544234379357251L;

	public static final String URI = "operation/activity-recommend";

	private Member createMember;
	private Member recommendMember;
	private Organization organization;

	private String source;

	public Member getCreateMember() {
		return createMember;
	}

	public void setCreateMember(Member createMember) {
		this.createMember = createMember;
	}

	public Member getRecommendMember() {
		return recommendMember;
	}

	public void setRecommendMember(Member recommendMember) {
		this.recommendMember = recommendMember;
	}

	public Organization getOrganization() {
		return organization;
	}

	public void setOrganization(Organization organization) {
		this.organization = organization;
	}


	public String getSource() {
		return source;
	}

	public void setSource(String source) {
		this.source = source;
	}
}
