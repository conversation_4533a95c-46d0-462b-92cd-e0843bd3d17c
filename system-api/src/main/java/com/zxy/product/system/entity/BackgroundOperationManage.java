package com.zxy.product.system.entity;

import com.zxy.product.system.jooq.tables.pojos.BackgroundOperationManageEntity;

/**
 * 后台运营管理,实体类
 */
public class BackgroundOperationManage extends BackgroundOperationManageEntity {

    public static final int TYPE_HOT_WORD =0;//搜索热词
    public static final int TYPE_HOT_CONTENT=1;//热门内容
    public static final int TYPE_RECOMMEND=2;//推荐内容
    public static final Integer TYPE_SENSITIVE_WORD = 3;// 敏感词
    public static final int IS_BLACKLIST_NO=0;//是黑名单
    public static final int IS_BLACKLIST_YES=1;//是黑名单
    public static final int BUSINESS_TYPE_HOT_WORD=0;//热词
    public static final int BUSINESS_TYPE_ISSUE=1;//问题
    public static final int BUSINESS_TYPE_ARTICLE=2;//文章
    public static final int BUSINESS_TYPE_COURSE=3;//课程
    public static final int BUSINESS_TYPE_SUBJECT=4;//专题
    public static final int BUSINESS_TYPE_LIVE=5;//直播
    public static final int SOURCE_MACHINE  =0;//机器
    public static final int SOURCE_ARTIFICIAL =1;//人工

}
