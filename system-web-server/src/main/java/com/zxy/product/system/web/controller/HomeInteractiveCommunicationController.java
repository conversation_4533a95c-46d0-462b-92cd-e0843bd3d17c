package com.zxy.product.system.web.controller;

import com.google.common.collect.ImmutableMap;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.common.restful.RequestContext;
import com.zxy.common.restful.annotation.Param;
import com.zxy.common.restful.json.JSON;
import com.zxy.common.restful.security.Permitted;
import com.zxy.common.restful.security.Subject;
import com.zxy.product.system.api.homeconfig.HomeInteractiveCommunicationService;
import com.zxy.product.system.entity.HomeInteractiveCommunication;
import com.zxy.product.system.entity.Member;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;
import java.util.Map;

/**
 * 首页互动交流配置Controller
 * <AUTHOR>
 * @date 2025年07月14日 14:50
 */
@Controller
@RequestMapping("/home-interactive-communication")
public class HomeInteractiveCommunicationController {

    private HomeInteractiveCommunicationService homeInteractiveCommunicationService;

    @Autowired
    public void setHomeInteractiveCommunicationService(HomeInteractiveCommunicationService homeInteractiveCommunicationService) {
        this.homeInteractiveCommunicationService = homeInteractiveCommunicationService;
    }

    // 查询 - 分页查询
    @RequestMapping(value = "/pages", method = RequestMethod.GET)
    @Permitted
    @Param(name = "moduleConfigId", required = true)
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @JSON("*")
    @JSON("items.(id,isDisplay,moduleConfigId,questionId,title,type,createMemberId,questionCreateTime,createMemberFullName)")
    public PagedResult<HomeInteractiveCommunication> findPage(RequestContext context, Subject<Member> subject, @RequestHeader String authorization) {
        return homeInteractiveCommunicationService.findPageFromCache(
            subject.getCurrentUserId(),
            authorization,
            context.getInteger("page"),
            context.getInteger("pageSize"),
            context.getString("moduleConfigId"));
    }

    // 新增
    @RequestMapping(method = RequestMethod.POST)
    @Permitted
    @Param(name = "moduleConfigId", required = true)
    @Param(name = "questionId", required = true)
    @JSON("*")
    public Map<String, Object> insert(RequestContext context, Subject<Member> subject, @RequestHeader String authorization) {
        HomeInteractiveCommunication result = homeInteractiveCommunicationService.insertToCache(
            subject.getCurrentUserId(),
            authorization,
            context.getString("moduleConfigId"),
            context.getString("questionId"),
            0); // 默认不展示

        if (result == null) {
            // 如果返回null，说明该questionId已存在，返回重复提示
            return ImmutableMap.of("success", false, "message", "该记录已存在，无法重复添加");
        }

        return ImmutableMap.of("success", true, "id", result.getId());
    }

    // 批量新增
    @RequestMapping(value = "/batch", method = RequestMethod.POST)
    @Permitted
    @Param(name = "moduleConfigId", required = true)
    @Param(name = "questionIds", required = true)
    @JSON("*")
    public Map<String, Object> batchInsert(RequestContext context, Subject<Member> subject, @RequestHeader String authorization) {
        String questionIdsStr = context.getString("questionIds");
        List<String> questionIdList = java.util.Arrays.asList(questionIdsStr.split(","));
        List<HomeInteractiveCommunication> results = homeInteractiveCommunicationService.batchInsertToCache(
            subject.getCurrentUserId(),
            authorization,
            context.getString("moduleConfigId"),
            questionIdList,
            0); // 默认不展示

        int totalRequested = questionIdList.size();
        int actualInserted = results.size();
        int duplicateSkipped = totalRequested - actualInserted;

        return ImmutableMap.of(
            "success", true,
            "totalRequested", totalRequested,
            "actualInserted", actualInserted,
            "duplicateSkipped", duplicateSkipped,
            "message", duplicateSkipped > 0 ?
                String.format("成功添加%d条记录，跳过%d条重复记录", actualInserted, duplicateSkipped) :
                String.format("成功添加%d条记录", actualInserted)
        );
    }

    // 修改
    @RequestMapping(value = "/{id}", method = RequestMethod.PUT)
    @Permitted
    @Param(name = "id", required = true)
    @Param(name = "moduleConfigId", required = true)
    @Param(name = "isDisplay", type = Integer.class, required = true)
    @JSON("*")
    public Map<String, Object> update(RequestContext context, Subject<Member> subject, @RequestHeader String authorization) {
        return homeInteractiveCommunicationService.updateToCache(
            subject.getCurrentUserId(),
            authorization,
            context.getString("id"),
            context.getString("moduleConfigId"),
            context.getInteger("isDisplay"));
    }

    // 删除
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Permitted
    @Param(name = "id", required = true)
    @JSON("*")
    public Map<String, String> delete(RequestContext context, Subject<Member> subject, @RequestHeader String authorization) {
        String id = homeInteractiveCommunicationService.deleteFromCache(
            subject.getCurrentUserId(),
            authorization,
            context.getString("id"));
        return ImmutableMap.of("id", id);
    }

    // 查询详情
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Permitted
    @Param(name = "id", required = true)
    @JSON("*")
    public HomeInteractiveCommunication get(RequestContext context, Subject<Member> subject, @RequestHeader String authorization) {
        return homeInteractiveCommunicationService.getFromCache(
            subject.getCurrentUserId(),
            authorization,
            context.getString("id"));
    }

    // 检查显示状态
    @RequestMapping(value = "/check-display-status", method = RequestMethod.GET)
    @Permitted
    @Param(name = "ids", required = true)
    @JSON("*")
    public List<Map<String, Object>> checkDisplayStatus(RequestContext context, Subject<Member> subject, @RequestHeader String authorization) {
        return homeInteractiveCommunicationService.checkDisplayStatusFromCache(
            subject.getCurrentUserId(),
            authorization,
            context.getString("ids"));
    }
}
