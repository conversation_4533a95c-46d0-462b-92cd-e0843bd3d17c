package com.zxy.product.system.content;

/**
 * @user tianjun 消息常量
 */
public class MessageTypeContent {

    /**
     * 系统管理 1
     * 人资模块(HR) 2
     * 考试 3
     * 课程模块(COURSE) 4
     * 08 questionnaire 班级问卷
     */

    /**
     * 系统定时发送消息
     */
    public static final int SYSTEM_SCHEDULE_MESSAGE = 10001; //系统定时发送消息

    // 组织
    public static final int SYSTEM_ORGANIZATION_INSERT = 10101;
    public static final int SYSTEM_ORGANIZATION_DELETE = 10102;
    public static final int SYSTEM_ORGANIZATION_UPDATE = 10103;
    public static final int SYSTEM_ORGANIZATION_DETAIL_CHANGE = 10104;
    public static final int SYSTEM_ORGANIZATION_UPDATE_DETAIL_CHANGE_SUCCESS = 10105;
    public static final int SYSTEM_ORGANIZATION_BATCH_INSERT = 10106;

    // 授权
    public static final int SYSTEM_GRANT_INSERT = 10201;
    public static final int SYSTEM_GRANT_DELETE = 10202;
    public static final int SYSTEM_GRANT_UPDATE = 10203;
    public static final int SYSTEM_GRANT_CHANGED = 10204; // 代表授权本身自己的变化处理完成
    public static final int SYSTEM_MEMBER_GRANT_CLEAR = 10205; // 代表清理用户的所有授权信息
    public static final int SYSTEM_GRANT_MEMBER_CLEAR = 10206; // 代表清理一个特定授权的用户
    public static final int SYSTEM_GRANTDETAIL_UPDATE = 10207; // 代表授权本身自己的变化影响到授权详情的变化
    public static final int SYSTEM_GRANTDETAIL_INFLUENCED = 10208; // 代表菜单,角色,组织,人员变更影响到授权详情的变化
    public static final int SYSTEM_GRANT_HISTORY_SAVE = 10209; // 保存授权历史

    /**
     *  系统表自己的节点明细表变化的时候
     *  id = 节点id
     */
    public static final int SYSTEM_ORGANIZATION_DETAIL_UPDATE = 10304;

    public static final int SYSTEM_MENU_INSERT = 10401;
    public static final int SYSTEM_MENU_DELETE = 10402;
    public static final int SYSTEM_MENU_UPDATE = 10403;

    public static final int SYSTEM_ROLE_INSERT = 10501;
    public static final int SYSTEM_ROLE_DELETE = 10502;
    public static final int SYSTEM_ROLE_UPDATE = 10503;
    public static final int SYSTEM_ROLE_UPDATE_RELEATED = 10504;

    public static final int SYSTEM_COMMENT_INFO_INSERT = 10601; // 评论
    public static final int SYSTEM_COMMENT_REPLY_INSERT = 10701; // 回复
    public static final int SYSTEM_COMMENT_REPLY_AUDIT = 10702; // 回复审核
    public static final int SYSTEM_COMMENT_PRAISE_INSERT = 10703; // 点赞

    public static final int SYSTEM_COLLECT_INSERT = 10801; // 收藏
    public static final int SYSTEM_COLLECT_DELETE = 10802;

    public static final int LOG_RESTFUL_INSERT = 88801; //restful调用日志

    /**
     * 广告管理
     */
    public static final int OPERATION_ADVERTISEMENT_INSERT = 10801;
    public static final int OPERATION_ADVERTISEMENT_DELETE = 10802;
    public static final int OPERATION_ADVERTISEMENT_UPDATE = 10803;

    public static final int OPERATION_ADVERTISEMENT_PUBLISH = 10804;
    public static final int OPERATION_ADVERTISEMENT_CANCEL = 10805;

    // 话题
    public static final int SYSTEM_TOPIC_INSERT = 10901;
    public static final int SYSTEM_TOPIC_UPDATE = 10902;
    public static final int SYSTEM_TOPIC_DELETE = 10903;
    public static final int SYSTEM_TOPIC_EXPERT_INSERT = 10904;
    public static final int SYSTEM_TOPIC_MANAGER_INSERT = 10905;
    public static final int SYSTEM_TOPIC_EXPERT_UPDATE = 10906;
    public static final int SYSTEM_TOPIC_MANAGER_UPDATE = 10907;
    public static final int SYSTEM_TOPIC_REFERENCE = 10908;
    public static final int SYSTEM_TOPIC_UNREFERENCE = 10909;
    public static final int SYSTEM_TOPIC_REFERENCE_STATISTICS = 10910;
    public static final int SYSTEM_TOPIC_UNREFERENCE_STATISTICS = 10911;
    public static final int SYSTEM_TOPIC_RANK_SAVE = 10912;
    public static final int SYSTEM_TEMP_TOPIC_INSERT = 10913;
    public static final int SYSTEM_TEMP_TOPIC_DELETE = 10914;
    public static final int SYSTEM_TOPIC_INTELLIGENCE_UPDATE = 10915;
    public static final int SYSTEM_TOPIC_INTELLIGENCE_UPDATE_STATISTICS = 10916;

    // 话题类型
    public static final int SYSTEM_TOPIC_TYPE_INSERT = 11001;
    public static final int SYSTEM_TOPIC_TYPE_UPDATE = 11002;
    public static final int SYSTEM_TOPIC_TYPE_DELETE = 11003;

    // 组织扩展
    public static final int SYSTEM_EXTENTION_VALUE_INSERT = 11101;
    public static final int SYSTEM_EXTENTION_VALUE_DELETE = 11102;
    public static final int SYSTEM_EXTENTION_VALUE_UPDATE = 11103;

    // 消息发送
    public static final int SEND_MESSAGE_INNER = 11201;//发站内信
    public static final int SEND_MESSAGE_EMAIL = 11202;//发邮件
    public static final int SEND_MESSAGE_APP = 11203;//发app
    public static final int SEND_MESSAGE_SHORT = 11204;//发短信
    public static final int SEND_MESSAGE_SHORT_TEP = 61204;//发短信培训加的传手机号码
    public static final int SEND_MESSAGE_PUSH = 11205;//消息推送
    public static final int SEND_MESSAGE_VALIDATE_EMAIL = 11207; //邮箱验证
    public static final int SEND_MESSAGE_SMS_BY_MOBILE = 11208; //根据手机号发送短信
    public static final int SEND_MESSAGE_WARNING_EMAIL = 11209; //发送预警邮件
    public static final int SEND_MESSAGE_DYNAMIC_PASSWORD = 11210; //发送动态密码
    public static final int SEND_MESSAGE_AT_ME = 11211; //发送@我消息

    // 手动消息推送
    public static final int SEND_MANUAL_MESSAGE_INNER = 11211;//发站内信
    public static final int SEND_MANUAL_MESSAGE_EMAIL = 11212;//发邮件
    public static final int SEND_MANUAL_MESSAGE_APP = 11213;//发app
    public static final int SEND_MANUAL_MESSAGE_SHORT = 11214;//发短信
    public static final int AUDIENCE_ITEM_INSERT = 11206;
    public static final int SEND_MANUAL_USER_MESSAGE = 11215;// 手动推送失败用户

    // 言论审核
    public static final int SPEECH_AUDIT_INSERT = 11301;//保存待审核记录

    // 待办消息
    public static final int MSG_COUNT_ITEM = 11401; // 消息数量item有更新
    public static final int MSG_COUNT_ITEM_COMPLETE = 11402; // 消息数量item处理完毕

    // 消息模板
    public static final int TEMP_MSG_SENDSYSTEM = 11501;//调用sendSystem方法发送
    public static final int TEMP_MSG_SEND = 11502;//调用send方法发送

    // 数据权限配置
    public static final int SYSTEM_DATA_PERMISSION_INSERT = 11601;  // 新增
    public static final int SYSTEM_DATA_PERMISSION_UPDATE = 11602;  // 更新

    // 规则配置
    public static final int SYSTEM_RULE_CONFIG_INSERT = 11701;  // 新增
    public static final int SYSTEM_RULE_CONFIG_UPDATE = 11702;  // 更新

    // setting
    public static final int SYSTEM_SETTING_INSERT = 11801;  // 新增
    public static final int SYSTEM_SETTING_UPDATE = 11802;  // 更新

    // 积分
    public static final int SYSTEM_SCORE_RESULT_CHANGE = 14004;  // 积分规则中不需要判断次数
    public static final int SYSTEM_SCORE_RESULT_CHANGE_OF_NUM = 14005;  // 积分规则中需要判断次数
    public static final int SYSTEM_SOCRE_RESULT_CHANGE_FOR_CONSUMER = 14006; // 直接消费积分

    //2023新版积分,当我上线稳定之时,旧版的积分就可以不用了
    public static final int SYSTEM_POINT_CHANGE = 14007;

    /**
     * 问吧审核处理
     */
    public static final int BAR_AUDIT = 50901;//问题审核

    // 云中心同步
    public static final int HR_CLOUD_CENTER_SYNC_REQUEST_FIRST = 21700; // 首次同步请求
    public static final int HR_CLOUD_CENTER_SYNC_REQUEST_THEN = 21701; // 非首次同步请求
    public static final int HR_CLOUD_CENTER_SYNC_FIRST = 21702; // 首次同步
    public static final int HR_CLOUD_CENTER_SYNC_FIRST_FIX = 21703; // 修复首次同步
    public static final int HR_CLOUD_CENTER_SYNC_THEN = 21704; // 非首次同步
    public static final int HR_CLOUD_CENTER_COMPANY_ADD = 21705; // 公司新增
    public static final int HR_CLOUD_CENTER_SITE_ADD = 21706; // 站点新增
    public static final int HR_CLOUD_CENTER_SYNC_STATUS = 21707; // 同步状态

    // 课程
    public static final int COURSE_INFO_INSERT = 40101;
    public static final int COURSE_INFO_DELETE = 40102;
    public static final int COURSE_INFO_UPDATE = 40103;

    // 知识
    public static final int KNOWLEDGE_INFO_INSERT = 40901;
    public static final int KNOWLEDGE_INFO_DELETE = 40902;
    public static final int KNOWLEDGE_INFO_UPDATE = 40903;

    /**
     * 班级问卷
     */
    public static final int TRAIN_QUESTIONNAIRE_MINIAPP_INSERT = 60803;
    /**
     * 我的任务修改计划名称，封面路径
     */
    public static final int TRAIN_UPDATE_PROJECT_NAME = 61111;
    public static final int TRAIN_UPDATE_CLASS_COVER = 61112;

    /**
     * 同步班级数据到活动中
     */
    public static final int TRAIN_CLASS_NOTICE_FOR_ACTIVITY = 61121;//发布通知的时候
    public static final int TRAIN_CLASS_UPDATE_FOR_ACTIVITY = 61122;//班级更新的时候
    public static final int TRAIN_CLASS_DELETE_FOR_ACTIVITY = 61123;//更新班级的报名方式为非开放报名时。删除活动中改班级

    public static final int HR_REPORT_TO_SYSTEM_ORGANIZATION = 99501; // MIS同步组织
    public static final int IHR_SYNC_ORGANIZATION_OVER_NOTICE = 99601; // IHR同步组织完成通知

    //行为数据
    public static final int BEHAVIOR_DATA_COLLECT = 71111; // 行为数据采集

    //行为数据采集用户点击
    public static final int BEHAVIOR_DATA_COLLECT_INSERT = 81111; // 行为数据采集用户点击
    public static final int BEHAVIOR_DATA_COLLECT_DELETE=85555;//删除行为数据点击

    public static final int TOP20_TOPIC_TASK_MESSAGE = 90001;   //热点主题
    public static final int TOP20_TOPIC_YEAR_TASK_MESSAGE = 90002;   //近一年热点主题

    //问吧专家工作室人气值统计
    public static final int BAR_STUDIO_POPULAR_COMPUTE = 71201;
    /**
     * 智能图片 1006
     */
    /**
     * 分类、样式删除后。异步统计素材数量
     */
    public static final int SMART_IMG_MATERIAL_STATISTIC = 100601;

    public static final int IHR_SYNC_MONTH_FIRST = 712022; // IHR同步月学习数据上线初期同步--当年所有月,所有组织下所有人的数据
    public static final int IHR_SYNC_MONTH = 712023; // IHR同步月学习数据手工同步--当年所有月,指定组织,指定人员的数据
    public static final int IHR_SYNC_MONTH_TASK = 712024; // 当天,所有组织下所有人的数据

//    public static final int IHR_SYNC_MONTH_FIRST_CSV = 712025; // IHR同步月学习数据上线初期同步--当年所有月,所有组织下所有人的数据

    public static final int IHR_SYNC_MONTHLY_STUDY_CSV = 712026; // IHR同步月学习数据手工同步--当年所有月,指定组织,指定人员的数据
    public static final int IHR_SYNC_MONTHLY_STUDY_TASK_CSV = 712027; //当天,所有组织下所有人的数据

    public static final int IHR_SYNC_STUDY_STATUS_CSV = 712031;//IHR同步月学习数据手工同步--开始时间,结束时间,指定组织,指定人员的数据-M_HR_CUST_140010015

    //手动触发热词
    public static final int HAND_HOT_WORD = 800001;

    /**首页配置更新  ||  系统配置更新 */
    public static final int HOME_SYSTEM=1000001;
    public static final int HOME_CONFIG=1000002;
    public static final int HOME_CERTIFY_CONFIG=1000003;

    /**
     * 注册发送短信
     */
    public static final int SEND_MESSAGE_SHORT_REGISTER = 25039;

    /**
     * 注册审批逻辑
     */
    public static final int REGISTER_INSERT=25036;
    public static final int REGISTER_RESULT=25037;

    public static final int DATA_SUMMARY=26010;
}
