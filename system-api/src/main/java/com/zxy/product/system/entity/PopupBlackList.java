package com.zxy.product.system.entity;

import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.List;

@Document(collection = "popup_black_list")
public class PopupBlackList {

    public static final String MEMBER_ID = "member_id";
    public static final String APPLICABLE_TERMINAL = "applicable_terminal";
    public static final String POP_IDS = "pop_ids";
    @Id
    private ObjectId id;
    @Field("pop_ids")
    private List<String> popIds;
    @Field("member_id")
    private String memberId;
    @Field("applicable_terminal")
    private Integer applicableTerminal;


    public List<String> getPopIds() {
        return popIds;
    }

    public void setPopIds(List<String> popIds) {
        this.popIds = popIds;
    }

    public String getMemberId() {
        return memberId;
    }

    public void setMemberId(String memberId) {
        this.memberId = memberId;
    }

    public Integer getApplicableTerminal() {
        return applicableTerminal;
    }

    public void setApplicableTerminal(Integer applicableTerminal) {
        this.applicableTerminal = applicableTerminal;
    }
}
