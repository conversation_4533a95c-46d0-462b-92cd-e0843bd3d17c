package com.zxy.product.system.domain.vo.home.mini.banner;

import java.io.Serializable;

/**
 * 首页小Banner：覆写App回显VO
 * <AUTHOR>
 * @date 2024年11月25日 11:34
 */
public class MiniBannerAppVO extends MiniBannerParentVO implements Serializable {
    private static final long serialVersionUID = 355456791177056397L;

    /**小BANNER：业务Id*/
    private String businessId;

    /**小BANNER：业务类型*/
    private Integer businessType;

    /**小BANNER：配置Id*/
    private String id;

    /**小BANNER：URL链接地址*/
    private String linkAddress;

    /**小BANNER：URL类型*/
    private Integer linkType;

    /**小BANNER：标题*/
    private String title;

    /**小BANNER：App图片地址*/
    private String appImagePath;

    public String getBusinessId() { return businessId; }

    public void setBusinessId(String businessId) { this.businessId = businessId; }

    public Integer getBusinessType() { return businessType; }

    public void setBusinessType(Integer businessType) { this.businessType = businessType; }

    public String getId() { return id; }

    public void setId(String id) { this.id = id; }

    public String getLinkAddress() { return linkAddress; }

    public void setLinkAddress(String linkAddress) { this.linkAddress = linkAddress; }

    public Integer getLinkType() { return linkType; }

    public void setLinkType(Integer linkType) { this.linkType = linkType; }

    public String getTitle() { return title; }

    public void setTitle(String title) { this.title = title; }

    public String getAppImagePath() { return appImagePath; }

    public void setAppImagePath(String appImagePath) { this.appImagePath = appImagePath; }

    @Override
    public String toString() {
        return "MiniBannerAppVO{" +
                "businessId='" + businessId + '\'' +
                ", businessType=" + businessType +
                ", id='" + id + '\'' +
                ", linkAddress='" + linkAddress + '\'' +
                ", linkType=" + linkType +
                ", title='" + title + '\'' +
                ", appImagePath='" + appImagePath + '\'' +
                '}';
    }
}
