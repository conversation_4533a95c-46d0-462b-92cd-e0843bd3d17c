package com.zxy.product.system.entity;

import com.zxy.product.system.jooq.tables.pojos.SpeechSetEntity;

/**
 * 言论控制
 * <AUTHOR>
 *
 */
public class SpeechSet extends SpeechSetEntity{
    private static final long serialVersionUID = 9205732776864501139L;
    public static final String URI = "operation/speech-set";

    // 是否审核 - 不审核（默认）
    public static final  int STATUS_OFF = 0;
    // 是否审核 - 要审核
    public static final  int STATUS_ON = 1;

    //直播不需要审核
    public static final Integer GENSEE_AUDIT_NO = 0;

    //最近操作人
    private String memberName;

    public String getMemberName() {
        return memberName;
    }

    public void setMemberName(String memberName) {
        this.memberName = memberName;
    }

}
