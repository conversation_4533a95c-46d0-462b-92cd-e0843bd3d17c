package com.zxy.product.system.entity;

import java.util.ArrayList;
import java.util.List;

import com.zxy.product.system.jooq.tables.pojos.IntegralAspectEntity;

/**
 * Created by Eric on 2016/12/23.
 */
public class IntegralAspect extends IntegralAspectEntity {
    /**
     *
     */
    private static final long serialVersionUID = -2736550003633975714L;
    private List<IntegralRule> ruleList = new ArrayList<>();

    public List<IntegralRule> getRuleList() {
        return ruleList;
    }

    public void setRuleList(List<IntegralRule> ruleList) {
        this.ruleList = ruleList;
    }
}
