package com.zxy.product.system.domain.vo.home.Lecturer;

import java.io.Serializable;

/**
 * 首页讲师榜：覆写App回显VO
 * <AUTHOR>
 * @date 2024年12月02日 14:26
 */
public class LecturerAppVO extends LecturerParentVO implements Serializable {
    private static final long serialVersionUID = -5829022544037116704L;

    /**讲师榜：讲师VO*/
    private LecturerVO lecturer;

    @Override
    public String toString() {
        return "LecturerAppVO{" +
                "lecturer=" + lecturer +
                '}';
    }

    /**讲师榜：内置VO*/
    public static class LecturerVO implements Serializable{
        private static final long serialVersionUID = -5470356551737962250L;

        /**讲师榜：图片地址*/
        private String coverPath;

        /**讲师榜：姓名*/
        private String name;

        public String getCoverPath() { return coverPath; }

        public void setCoverPath(String coverPath) { this.coverPath = coverPath; }

        public String getName() { return name; }

        public void setName(String name) { this.name = name; }

        @Override
        public String toString() {
            return "LecturerVO{" +
                    "coverPath='" + coverPath + '\'' +
                    ", name='" + name + '\'' +
                    '}';
        }
    }
}
