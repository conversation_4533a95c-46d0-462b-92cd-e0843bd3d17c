package com.zxy.product.system.content;

import java.util.ArrayList;
import java.util.List;

public class IntegralRuleConstant {

    // 成功举报问题或言论获得{0}积分
    public static final String BAR_ACCUSE_ADD = "bar_accuse_add";
    // 成功申请成为专家获得{0}积分
    public static final String BAR_APPLY_EXPERT = "bar_apply_expert";
    // 成功申请一个话题获得{0}积分
    public static final String BAR_APPLY_TOPIC = "bar_apply_topic";
    // 问题被举报扣除{0}积 分(多人举报只扣除一次)
    public static final String BAR_BEEN_ACCUSE = "bar_been_accuse";
    // 我的问题被关注大于等于{1}人次获得{0}积分【旧】
    // 我的问题被关注一次获得{0}积分，关注超过{1}次不再获得积分
    public static final String BAR_BEEN_CARE = "bar_been_care";
    // 我的问题被收藏大于等于{1}人次获得{0}积分
    public static final String BAR_COLLECT_NUM = "bar_collect_num";
    // 我参与的讨论被点赞大于等于{1}人次获得{0}积分【旧】
    // 我参与的讨论被点赞一次获得{0}积分，浏览超过{1}次不再获得积分
    public static final String BAR_PRAISE_NUM = "bar_praise_num";
    // 成功参与问题讨论每次获得{0}分，回复超过{1}次不再累计得分
    public static final String BAR_DISCUSS = "bar_discuss";
    // 我的讨论被加精获得{0}积分
    public static final String BAR_DISCUSS_ESSENCE = "bar_discuss_essence";
    // 我的问题讨论数大于等于{1}人次获得{0}积分【旧】
    // 我的问题被讨论一次获得{0}积分，讨论超过{1}次不再获得积分
    public static final String BAR_DISCUSS_NUM = "bar_discuss_num";
    // 成功提出一个问题获得{0}积分【旧】
    // 成功提出一个问题获得{0}分，当天超过{1}次不再获得积分
    public static final String BAR_QUESTION = "bar_question";
    // 我的问题被关闭扣除{0}积分
    public static final String BAR_QUESTION_CLOSE = "bar_question_close";
    // 我的问题被删除扣除{0}积分
    public static final String BAR_QUESTION_DELETE = "bar_question_delete";
    // 我的问题被设置为精品获得{0}积分
    public static final String BAR_QUESTION_ESSENCE = "bar_question_essence";
    // 我的问题被浏览次数大于等于{1}人次获得{0}积分【旧】
    // 我的问题被浏览一次获得{0}积分，浏览超过{1}次不再获得积分
    public static final String BAR_VIEW_NUM = "bar_view_num";
    // 参与讨论每次获得{0}积分，讨论超过{1}次参与不再获得积分
    public static final String COMMENT_ATTEND = "comment_attend";
    // 我的讨论被设置为精华获得{0}积分
    public static final String COMMENT_ESSENCE = "comment_essence";
    // 成功举报言论获得{0}积分
    public static final String COMMENT_EXPOSE_ADD = "comment_expose_add";
    // 言论被成功举报扣除{0}分
    public static final String COMMENT_EXPOSE_REDUCE = "comment_expose_reduce";
    // 发表讨论每次获得{0}积分，超过{1}次发表讨论不再获得积分
    public static final String COMMENT_PUBLISH = "comment_publish";
    // 知识收藏数大于等于{1}人次得{0}分【旧】
    // 我的知识被收藏一次获得{0}分，收藏超过{1}次不再获得积分
    public static final String LIB_COLLECT = "lib_collect";
    // 知识被下载大于等于{1}人次得{0}分【旧】
    // 下载管理员（后台上传）的知识，管理员不加分； 下载用户（前台上传）的知识，积分加给对应的知识上传者
    public static final String LIB_DOWNLOAD = "lib_download";
    // 分享一篇文档或视频得{0}分【旧】
    // APP分享课程、专题、直播、知识获得{0}分，当天分享超过{1}次不再获得积分
    public static final String LIB_UPLOAD = "lib_upload";
    // 知识浏览数大于等于{1}人次得{0}分【旧】
    // 我的知识被浏览一次获得{0}分，浏览超过{1}次不再获得积分
    public static final String LIB_VIEW = "lib_view";
    // 下载知识消费积分
    public static final String LIB_DOWNLOAD_DELETE = "lib_download_del";
    // 完善个人简介获得{0}积分
    public static final String USER_CONTENT = "user_content";
    // 每人每天最多获得{0}积分
    public static final String USER_GET_MOST = "user_get_most";
    // 连续{1}天登录获得{0}积分【旧】
    // 登录一次获得{0}积分，当天登录超过{1}次不再获得积分
    public static final String USER_LOGIN = "user_login";
    // 完善手机号获得{0}积分
    public static final String USER_PHONE = "user_phone";
    // 成功获得头像获得{0}积分
    public static final String USER_PHOTO = "user_photo";
	// 完成一门课程获得{0}积分
    public static final String COURSE_COMPLETE = "course_complete";
	// 报名一门直播获得{0}积分
    public static final String GENSEE_APPOINTMENT = "gensee_appointment";
    // 取消报名直播扣除预约所得分数
    public static final String GENSEE_CANCEL_APPOINTMENT_DELETE = "gensee_cancel_appointment_del";
	// APP分享课程、专题、直播、知识获得{0}积分，当天分享超过{1}次不再获得积分
    public static final String SHARE_APP_ALL = "share_app_all";
    // 预约直播消耗相应分数
    public static final String GENSEE_APPOINTMENT_DELETE = "gensee_appointment_del";
    // 学习课程消耗相应分数
    public static final String COURSE_SEE_DELETE = "course_see_del";
    
    // 关注不足次数取消关注扣除相应分数
	public static final String BAR_BEEN_CARE_DELETE = "bar_been_care_delete";
    // 问题收藏不足次数取消收藏扣除相应分数
	public static final String BAR_COLLECT_NUM_DELETE = "bar_collect_num_delete";
    // 同一内容讨论不足次数删除讨论扣除相应分数
	public static final String COMMENT_PUBLISH_DELETE = "comment_publish_delete";
	// 讨论被取消精华扣除相应积分
	public static final String COMMENT_ESSENCE_DELETE = "comment_essence_delete";
    // 知识收藏不足次数取消收藏扣除相应分数
	public static final String LIB_COLLECT_DELETE = "lib_collect_delete";

    //积分商城签到领积分  每日成功签到获取{0}积分
    public static final String MALL_CHECK_IN = "mall_check_in";

    /**
     * 抽奖消耗积分
     */
    public static final String LOTTERY_CONSUME = "lottery_consumer";

    /**
     * 抽奖新增积分
     */
    public static final String LOTTERY_ADD_INTEGRAL = "lottery_add_integral" ;


    public static List<String> numberToAppOne = new ArrayList<>();
    
    public static List<String> numberToDay = new ArrayList<>();
    
    public static List<String> numberToBusiness = new ArrayList<>();
    
    public static List<String> reversibleToOperation = new ArrayList<>();
    
    public static List<String> substractListKeys = new ArrayList<>();
    
    public static List<String> checkedIsIntegralKeys = new ArrayList<>();

    static {
    	//全局只扣分一次
    	numberToAppOne.add(BAR_BEEN_ACCUSE);
    	numberToAppOne.add(COMMENT_EXPOSE_REDUCE);
    	numberToAppOne.add(LIB_DOWNLOAD_DELETE);
    	//需每日统计次数
    	numberToDay.add(BAR_QUESTION);
    	numberToDay.add(LIB_UPLOAD);
    	numberToDay.add(USER_LOGIN);
    	numberToDay.add(SHARE_APP_ALL);
    	//需统计用户与该业务的次数
    	numberToBusiness.add(COMMENT_PUBLISH);
    	numberToBusiness.add(BAR_BEEN_CARE);
    	numberToBusiness.add(BAR_COLLECT_NUM);
    	numberToBusiness.add(BAR_PRAISE_NUM);
    	numberToBusiness.add(BAR_DISCUSS);
    	numberToBusiness.add(BAR_DISCUSS_NUM);
    	numberToBusiness.add(BAR_VIEW_NUM);
    	numberToBusiness.add(COMMENT_ATTEND);
    	numberToBusiness.add(LIB_COLLECT);
    	numberToBusiness.add(LIB_VIEW);

    	// 可逆操作扣分集合【f_rule_code 为 1 OR 3】
    	reversibleToOperation.add(BAR_BEEN_CARE_DELETE);
    	reversibleToOperation.add(BAR_COLLECT_NUM_DELETE);
    	reversibleToOperation.add(COMMENT_PUBLISH_DELETE);
    	reversibleToOperation.add(COMMENT_ESSENCE_DELETE);
    	reversibleToOperation.add(LIB_COLLECT_DELETE);
    	
    	//不需要判断次数的减积分操作
        substractListKeys.add(BAR_QUESTION_DELETE);
        substractListKeys.add(BAR_QUESTION_CLOSE);
        //基于全局扣分之上
        substractListKeys.add(BAR_BEEN_ACCUSE);
        substractListKeys.add(COMMENT_EXPOSE_REDUCE);
        substractListKeys.add(LIB_DOWNLOAD_DELETE);

        // 全局只能加一次分的业务key
        checkedIsIntegralKeys.add(USER_CONTENT);
        checkedIsIntegralKeys.add(USER_PHOTO);
        checkedIsIntegralKeys.add(USER_PHONE);
        checkedIsIntegralKeys.add(BAR_ACCUSE_ADD);
        checkedIsIntegralKeys.add(BAR_APPLY_EXPERT);
        checkedIsIntegralKeys.add(BAR_APPLY_TOPIC);
        checkedIsIntegralKeys.add(BAR_DISCUSS_ESSENCE);
        checkedIsIntegralKeys.add(BAR_QUESTION_ESSENCE);
        checkedIsIntegralKeys.add(COMMENT_EXPOSE_ADD);
        checkedIsIntegralKeys.add(COURSE_COMPLETE);
        checkedIsIntegralKeys.add(GENSEE_APPOINTMENT);
    }

}