package com.zxy.product.system.domain.dto;

import java.io.Serializable;
import java.util.Optional;

/**
 * 首页：查询现阶段回显版本首页DTO
 * <AUTHOR>
 * @date 2024年10月30日 16:22
 */
public class EchoHomeDTO implements Serializable {
    private static final long serialVersionUID = -8896077683529973091L;

    /**域名*/
    private String domain;

    /**虚拟空间Id（非必填）*/
    private Optional<String> spaceIdOpt;

    /**类型 0机构首页 1虚拟空间（非必填）*/
    private Optional<Integer> typeOpt;

    /**首页配置主Id（非必填）*/
    private Optional<String> cfgIdOpt;

    /**组织Id（非必填）*/
    private Optional<String> orgIdOpt;

    public String getDomain() { return domain; }

    public void setDomain(String domain) { this.domain = domain; }

    public Optional<String> getSpaceIdOpt() { return spaceIdOpt; }

    public void setSpaceIdOpt(Optional<String> spaceIdOpt) { this.spaceIdOpt = spaceIdOpt; }

    public Optional<Integer> getTypeOpt() { return typeOpt; }

    public void setTypeOpt(Optional<Integer> typeOpt) { this.typeOpt = typeOpt; }

    public Optional<String> getCfgIdOpt() { return cfgIdOpt; }

    public void setCfgIdOpt(Optional<String> cfgIdOpt) { this.cfgIdOpt = cfgIdOpt; }

    public Optional<String> getOrgIdOpt() { return orgIdOpt; }

    public void setOrgIdOpt(Optional<String> orgIdOpt) { this.orgIdOpt = orgIdOpt; }

    @Override
    public String toString() {
        return "EchoHomeDTO{" +
                "domain='" + domain + '\'' +
                ", spaceIdOpt=" + spaceIdOpt +
                ", typeOpt=" + typeOpt +
                ", cfgIdOpt=" + cfgIdOpt +
                ", orgIdOpt=" + orgIdOpt +
                '}';
    }
}
