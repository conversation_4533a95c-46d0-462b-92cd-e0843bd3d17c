/*
 * This file is generated by jOOQ.
 */
package com.zxy.product.system.jooq.tables.pojos;


import com.zxy.common.base.entity.BaseEntity;
import com.zxy.product.system.jooq.tables.interfaces.IHomeInteractiveCommunication;

import javax.annotation.Generated;


/**
 * 首页互动交流配置表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.12.4"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class HomeInteractiveCommunicationEntity extends BaseEntity implements IHomeInteractiveCommunication {

    private static final long serialVersionUID = 1L;

    private String  moduleConfigId;
    private String  questionId;
    private Integer    isDisplay;

    public HomeInteractiveCommunicationEntity() {}

    public HomeInteractiveCommunicationEntity(IHomeInteractiveCommunication value) {
        this.moduleConfigId = value.getModuleConfigId();
        this.questionId = value.getQuestionId();
        this.isDisplay = value.getIsDisplay();
    }

    public HomeInteractiveCommunicationEntity(
        String  id,
        String  moduleConfigId,
        String  questionId,
        Integer    isDisplay,
        Long    createTime
    ) {
        super.setId(id);
        this.moduleConfigId = moduleConfigId;
        this.questionId = questionId;
        this.isDisplay = isDisplay;
        super.setCreateTime(createTime);
    }

    @Override
    public String getId() {
        return super.getId();
    }

    @Override
    public void setId(String id) {
        super.setId(id);
    }

    @Override
    public String getModuleConfigId() {
        return this.moduleConfigId;
    }

    @Override
    public void setModuleConfigId(String moduleConfigId) {
        this.moduleConfigId = moduleConfigId;
    }

    @Override
    public String getQuestionId() {
        return this.questionId;
    }

    @Override
    public void setQuestionId(String questionId) {
        this.questionId = questionId;
    }



    @Override
    public Integer getIsDisplay() {
        return this.isDisplay;
    }

    @Override
    public void setIsDisplay(Integer isDisplay) {
        this.isDisplay = isDisplay;
    }

    @Override
    public Long getCreateTime() {
        return super.getCreateTime();
    }

    @Override
    public void setCreateTime(Long createTime) {
        super.setCreateTime(createTime);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("HomeInteractiveCommunicationEntity (");

        sb.append(getId());
        sb.append(", ").append(moduleConfigId);
        sb.append(", ").append(questionId);
        sb.append(", ").append(isDisplay);
        sb.append(", ").append(getCreateTime());

        sb.append(")");
        return sb.toString();
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    @Override
    public void from(IHomeInteractiveCommunication from) {
        setId(from.getId());
        setModuleConfigId(from.getModuleConfigId());
        setQuestionId(from.getQuestionId());
        setIsDisplay(from.getIsDisplay());
        setCreateTime(from.getCreateTime());
    }

    @Override
    public <E extends IHomeInteractiveCommunication> E into(E into) {
        into.from(this);
        return into;
    }

    public void forInsert() {
        this.setId(java.util.UUID.randomUUID().toString());
        this.setCreateTime(System.currentTimeMillis());
    }

    public static final <E extends HomeInteractiveCommunicationEntity> org.jooq.RecordMapper<? extends org.jooq.Record, E> createMapper(Class<E> type) {
        return new org.jooq.RecordMapper<org.jooq.Record, E>() {
            @Override
            public E map(org.jooq.Record record) {
                com.zxy.product.system.jooq.tables.records.HomeInteractiveCommunicationRecord r = new com.zxy.product.system.jooq.tables.records.HomeInteractiveCommunicationRecord();
                org.jooq.Row row = record.fieldsRow();
                    if(row.indexOf(com.zxy.product.system.jooq.tables.HomeInteractiveCommunication.HOME_INTERACTIVE_COMMUNICATION.ID) > -1){
                        r.setValue(com.zxy.product.system.jooq.tables.HomeInteractiveCommunication.HOME_INTERACTIVE_COMMUNICATION.ID, record.getValue(com.zxy.product.system.jooq.tables.HomeInteractiveCommunication.HOME_INTERACTIVE_COMMUNICATION.ID));
                    }
                    if(row.indexOf(com.zxy.product.system.jooq.tables.HomeInteractiveCommunication.HOME_INTERACTIVE_COMMUNICATION.MODULE_CONFIG_ID) > -1){
                        r.setValue(com.zxy.product.system.jooq.tables.HomeInteractiveCommunication.HOME_INTERACTIVE_COMMUNICATION.MODULE_CONFIG_ID, record.getValue(com.zxy.product.system.jooq.tables.HomeInteractiveCommunication.HOME_INTERACTIVE_COMMUNICATION.MODULE_CONFIG_ID));
                    }
                    if(row.indexOf(com.zxy.product.system.jooq.tables.HomeInteractiveCommunication.HOME_INTERACTIVE_COMMUNICATION.QUESTION_ID) > -1){
                        r.setValue(com.zxy.product.system.jooq.tables.HomeInteractiveCommunication.HOME_INTERACTIVE_COMMUNICATION.QUESTION_ID, record.getValue(com.zxy.product.system.jooq.tables.HomeInteractiveCommunication.HOME_INTERACTIVE_COMMUNICATION.QUESTION_ID));
                    }
                    if(row.indexOf(com.zxy.product.system.jooq.tables.HomeInteractiveCommunication.HOME_INTERACTIVE_COMMUNICATION.IS_DISPLAY) > -1){
                        r.setValue(com.zxy.product.system.jooq.tables.HomeInteractiveCommunication.HOME_INTERACTIVE_COMMUNICATION.IS_DISPLAY, record.getValue(com.zxy.product.system.jooq.tables.HomeInteractiveCommunication.HOME_INTERACTIVE_COMMUNICATION.IS_DISPLAY));
                    }
                    if(row.indexOf(com.zxy.product.system.jooq.tables.HomeInteractiveCommunication.HOME_INTERACTIVE_COMMUNICATION.CREATE_TIME) > -1){
                        r.setValue(com.zxy.product.system.jooq.tables.HomeInteractiveCommunication.HOME_INTERACTIVE_COMMUNICATION.CREATE_TIME, record.getValue(com.zxy.product.system.jooq.tables.HomeInteractiveCommunication.HOME_INTERACTIVE_COMMUNICATION.CREATE_TIME));
                    }
                try {
                    E pojo = type.newInstance();
                    pojo.from(r);
                    return pojo;
                } catch (Exception e) {
                    e.printStackTrace(); // ignored
                }
                return null;
            }
        };
    }}
