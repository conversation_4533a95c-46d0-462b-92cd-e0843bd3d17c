package com.zxy.product.system.content;

import java.util.regex.Pattern;

/**
 * @Author: zhouyang
 * @Date: 2021/3/18 17:23
 */
public class ParamConstant {

    private ParamConstant() {

    }

    public static final String ID = "id";

    public static final String SLASH = "/";

    public static final String APP_COURSES = "appCourses";

    public static final String HASH = "#";

    public static final String ORGANIZATION_ID = "organizationId";

    public static final String CONFIG_ID = "configId";

    public static final String IDS = "ids";

    public static final String COMMA = ",";

    public static final String UTF_8 = "UTF-8";

    public static final Integer BATCH_SIZE = 1000;

    public static final Integer LENGTH_MIN = 0;

    public static final Integer COST_MAX = 1000000;

    public static final Integer TEACH_AVERAGE_MAX = 100;

    public static final Pattern NUMBER_PATTERN = Pattern.compile("^[-\\+]?[\\d]*$");

    public static final Integer SUCCESS_RESULT = 1;

    public static final Integer FAIL_RESULT = 0;

    public static final String PAGE = "page";
    public static final String PAGE_SIZE = "pageSize";
    public static final String PUSH_ID = "pushId";
    public static final String AUDIENCE_ITEMS = "audienceItems";
    public static final String RESULT = "result";
    public static final String SUCCESS = "success";

    /**
     * 查询学币明细时的入口类型
     */
    public static final String ENTRANCE_TYPE = "entranceType";

    /**
     * 封面关键字
     */
    public static final String COVER_KEY = "coverKey";

    /**
     * 悦课支付
     */
    public static final String ORGANIZATION_CODE = "orgCode";
    public static final String NAME = "name";
    public static final String SIGN = "sign";
    public static final String COURSE_ID = "courseId";
    public static final String COURSE_NAME = "goodsName";
    public static final String COVER_PATH = "goodsCover";
    public static final String CUSTOM_PRICE = "customPrice";
    public static final String PRICE = "price";
    public static final String ORDER_NO = "orderNo";
    public static final String PAY_NO = "payNo";
    public static final String SAAS_MEMBER_ID = "saasMemberId";
    public static final String COMPANY_ID = "companyId";
    public static final String STATUS = "status";
    public static final String PAY_FINISH_TIME = "payFinishTime";
    public static final String MEMBER_NAME = "memberName";
    public static final String MEMBER_READ_NAME = "memberReadName";
    public static final String FINISH_STATUS = "finishStatus";
    public static final String ORGANIZATION_PATH = "organizationPath";
    public static final String MEMBER_ID = "memberId";

}