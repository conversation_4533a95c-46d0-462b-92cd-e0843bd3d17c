package com.zxy.product.system.entity;

import com.zxy.product.system.jooq.tables.pojos.CommentReplyEntity;

/**
 * <AUTHOR>
 *
 */
public class CommentReply extends CommentReplyEntity {

    private static final long serialVersionUID = -2685958890954717687L;

    public static final int AUDIT_STATUS_WAIT = 0; // 未审核
    public static final int AUDIT_STATUS_PASS = 1; // 审核通过
    public static final int AUDIT_STATUS_REFUSE = 1; //审核拒绝

    public static final Integer TOP_STATUS_HIDE = 1; // 隐藏
    public static final Integer TOP_STATUS_SHOW = 0; // 显示

    public static final Integer READ_STATUS_NO = 0; // 未读
    public static final Integer READ_STATUS_YES = 1; // 已读

    public static final int DELETE_FLASE = 0;    //删除状态：未删除
    public static final int DELETE_TRUE = 1;  //删除状态，已删除

    public static final int ACCUSE_STATUS_NO = 0; // 未被举报
    public static final int ACCUSE_STATUS_YES = 1; // 被举报

    private String avatarId; // 头像文件id
    private String memberName; // 回复用户名称
    private String toMemberName; // 被回复的用户名称
    private String praiseId; // 点赞id
    private String createTimeStr;//创建时间显示

    private String objectId;//审核对象id

    private SpeechAudit speechAudit;

    public SpeechAudit getSpeechAudit() {
        return speechAudit;
    }

    public void setSpeechAudit(SpeechAudit speechAudit) {
        this.speechAudit = speechAudit;
    }

    public String getObjectId() {
        return objectId;
    }

    public void setObjectId(String objectId) {
        this.objectId = objectId;
    }

    public String getAvatarId() {
        return avatarId;
    }
    public void setAvatarId(String avatarId) {
        this.avatarId = avatarId;
    }
    public String getMemberName() {
        return memberName;
    }
    public void setMemberName(String memberName) {
        this.memberName = memberName;
    }
    public String getToMemberName() {
        return toMemberName;
    }
    public void setToMemberName(String toMemberName) {
        this.toMemberName = toMemberName;
    }
    public String getPraiseId() {
        return praiseId;
    }
    public void setPraiseId(String praiseId) {
        this.praiseId = praiseId;
    }
    public String getCreateTimeStr() {
        return createTimeStr;
    }
    public void setCreateTimeStr(String createTimeStr) {
        this.createTimeStr = createTimeStr;
    }

}
