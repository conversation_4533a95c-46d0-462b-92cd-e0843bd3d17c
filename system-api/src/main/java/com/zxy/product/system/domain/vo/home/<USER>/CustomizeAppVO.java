package com.zxy.product.system.domain.vo.home.customize;

import java.io.Serializable;

/**
 * 首页自定义：App回显VO
 * <AUTHOR>
 * @date 2024年11月25日 15:55
 */
public class CustomizeAppVO extends CustomizeParentVO implements Serializable {
    private static final long serialVersionUID = 4456349044527378973L;

    /**自定义：配置Id*/
    private String id;

    /**自定义：名称*/
    private String name;

    /**自定义：数据Id*/
    private String dataId;

    /**自定义：数据名称*/
    private String dataName;

    /**自定义：数据类型*/
    private Integer dataType;

    /**自定义：图片地址*/
    private String coverPath;

    /**自定义：图片URL跳转链接*/
    private String url;

    /**自定义：资源简介*/
    private String dataExt;

    public String getId() { return id; }

    public void setId(String id) { this.id = id; }

    public String getName() { return name; }

    public void setName(String name) { this.name = name; }

    public String getDataId() { return dataId; }

    public void setDataId(String dataId) { this.dataId = dataId; }

    public String getDataName() { return dataName; }

    public void setDataName(String dataName) { this.dataName = dataName; }

    public Integer getDataType() { return dataType; }

    public void setDataType(Integer dataType) { this.dataType = dataType; }

    public String getCoverPath() { return coverPath; }

    public void setCoverPath(String coverPath) { this.coverPath = coverPath; }

    public String getUrl() { return url; }

    public void setUrl(String url) { this.url = url; }

    public String getDataExt() { return dataExt; }

    public void setDataExt(String dataExt) { this.dataExt = dataExt; }

    @Override
    public String toString() {
        return "CustomizeAppVO{" +
                "id='" + id + '\'' +
                ", name='" + name + '\'' +
                ", dataId='" + dataId + '\'' +
                ", dataName='" + dataName + '\'' +
                ", dataType=" + dataType +
                ", coverPath='" + coverPath + '\'' +
                ", url='" + url + '\'' +
                ", dataExt='" + dataExt + '\'' +
                '}';
    }
}

