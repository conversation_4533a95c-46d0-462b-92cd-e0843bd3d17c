/*
 * This file is generated by jOOQ.
 */
package com.zxy.product.system.jooq.tables.records;


import com.zxy.product.system.jooq.tables.HomeInteractiveCommunication;
import com.zxy.product.system.jooq.tables.interfaces.IHomeInteractiveCommunication;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record5;
import org.jooq.Row5;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 首页互动交流配置表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.12.4"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class HomeInteractiveCommunicationRecord extends UpdatableRecordImpl<HomeInteractiveCommunicationRecord> implements Record5<String, String, String, Integer, Long>, IHomeInteractiveCommunication {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>system.t_home_interactive_communication.f_id</code>. 记录ID
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>system.t_home_interactive_communication.f_id</code>. 记录ID
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>system.t_home_interactive_communication.f_module_config_id</code>. 配置模块ID
     */
    @Override
    public void setModuleConfigId(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>system.t_home_interactive_communication.f_module_config_id</code>. 配置模块ID
     */
    @Override
    public String getModuleConfigId() {
        return (String) get(1);
    }

    /**
     * Setter for <code>system.t_home_interactive_communication.f_question_id</code>. 问吧记录id
     */
    @Override
    public void setQuestionId(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>system.t_home_interactive_communication.f_question_id</code>. 问吧记录id
     */
    @Override
    public String getQuestionId() {
        return (String) get(2);
    }

    /**
     * Setter for <code>system.t_home_interactive_communication.f_is_display</code>. 是否展示:0 不展示；1 展示
     */
    @Override
    public void setIsDisplay(Integer value) {
        set(3, value);
    }

    /**
     * Getter for <code>system.t_home_interactive_communication.f_is_display</code>. 是否展示:0 不展示；1 展示
     */
    @Override
    public Integer getIsDisplay() {
        return (Integer) get(3);
    }

    /**
     * Setter for <code>system.t_home_interactive_communication.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(4, value);
    }

    /**
     * Getter for <code>system.t_home_interactive_communication.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(4);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record5 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row5<String, String, String, Integer, Long> fieldsRow() {
        return (Row5) super.fieldsRow();
    }

    @Override
    public Row5<String, String, String, Integer, Long> valuesRow() {
        return (Row5) super.valuesRow();
    }

    @Override
    public Field<String> field1() {
        return HomeInteractiveCommunication.HOME_INTERACTIVE_COMMUNICATION.ID;
    }

    @Override
    public Field<String> field2() {
        return HomeInteractiveCommunication.HOME_INTERACTIVE_COMMUNICATION.MODULE_CONFIG_ID;
    }

    @Override
    public Field<String> field3() {
        return HomeInteractiveCommunication.HOME_INTERACTIVE_COMMUNICATION.QUESTION_ID;
    }

    @Override
    public Field<Integer> field4() {
        return HomeInteractiveCommunication.HOME_INTERACTIVE_COMMUNICATION.IS_DISPLAY;
    }

    @Override
    public Field<Long> field5() {
        return HomeInteractiveCommunication.HOME_INTERACTIVE_COMMUNICATION.CREATE_TIME;
    }

    @Override
    public String value1() {
        return getId();
    }

    @Override
    public String value2() {
        return getModuleConfigId();
    }

    @Override
    public String value3() {
        return getQuestionId();
    }

    @Override
    public Integer value4() {
        return getIsDisplay();
    }

    @Override
    public Long value5() {
        return getCreateTime();
    }

    @Override
    public HomeInteractiveCommunicationRecord value1(String value) {
        setId(value);
        return this;
    }

    @Override
    public HomeInteractiveCommunicationRecord value2(String value) {
        setModuleConfigId(value);
        return this;
    }

    @Override
    public HomeInteractiveCommunicationRecord value3(String value) {
        setQuestionId(value);
        return this;
    }

    @Override
    public HomeInteractiveCommunicationRecord value4(Integer value) {
        setIsDisplay(value);
        return this;
    }

    @Override
    public HomeInteractiveCommunicationRecord value5(Long value) {
        setCreateTime(value);
        return this;
    }

    @Override
    public HomeInteractiveCommunicationRecord values(String value1, String value2, String value3, Integer value4, Long value5) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    @Override
    public void from(IHomeInteractiveCommunication from) {
        setId(from.getId());
        setModuleConfigId(from.getModuleConfigId());
        setQuestionId(from.getQuestionId());
        setIsDisplay(from.getIsDisplay());
        setCreateTime(from.getCreateTime());
    }

    @Override
    public <E extends IHomeInteractiveCommunication> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached HomeInteractiveCommunicationRecord
     */
    public HomeInteractiveCommunicationRecord() {
        super(HomeInteractiveCommunication.HOME_INTERACTIVE_COMMUNICATION);
    }

    /**
     * Create a detached, initialised HomeInteractiveCommunicationRecord
     */
    public HomeInteractiveCommunicationRecord(String id, String moduleConfigId, String questionId, Integer isDisplay, Long createTime) {
        super(HomeInteractiveCommunication.HOME_INTERACTIVE_COMMUNICATION);

        set(0, id);
        set(1, moduleConfigId);
        set(2, questionId);
        set(3, isDisplay);
        set(4, createTime);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.system.jooq.tables.pojos.HomeInteractiveCommunicationEntity)) {
            return false;
        }
        com.zxy.product.system.jooq.tables.pojos.HomeInteractiveCommunicationEntity pojo = (com.zxy.product.system.jooq.tables.pojos.HomeInteractiveCommunicationEntity)source;
        pojo.into(this);
        return true;
    }
}
