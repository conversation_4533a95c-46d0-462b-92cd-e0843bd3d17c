/*
 * This file is generated by jOOQ.
 */
package com.zxy.product.system.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 首页互动交流配置表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.12.4"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IHomeInteractiveCommunication extends Serializable {

    /**
     * Setter for <code>system.t_home_interactive_communication.f_id</code>. 记录ID
     */
    public void setId(String value);

    /**
     * Getter for <code>system.t_home_interactive_communication.f_id</code>. 记录ID
     */
    public String getId();

    /**
     * Setter for <code>system.t_home_interactive_communication.f_module_config_id</code>. 配置模块ID
     */
    public void setModuleConfigId(String value);

    /**
     * Getter for <code>system.t_home_interactive_communication.f_module_config_id</code>. 配置模块ID
     */
    public String getModuleConfigId();

    /**
     * Setter for <code>system.t_home_interactive_communication.f_question_id</code>. 问吧记录id
     */
    public void setQuestionId(String value);

    /**
     * Getter for <code>system.t_home_interactive_communication.f_question_id</code>. 问吧记录id
     */
    public String getQuestionId();

    /**
     * Setter for <code>system.t_home_interactive_communication.f_is_display</code>. 是否展示:0 不展示；1 展示
     */
    public void setIsDisplay(Integer value);

    /**
     * Getter for <code>system.t_home_interactive_communication.f_is_display</code>. 是否展示:0 不展示；1 展示
     */
    public Integer getIsDisplay();

    /**
     * Setter for <code>system.t_home_interactive_communication.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>system.t_home_interactive_communication.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IHomeInteractiveCommunication
     */
    public void from(IHomeInteractiveCommunication from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IHomeInteractiveCommunication
     */
    public <E extends IHomeInteractiveCommunication> E into(E into);
}
