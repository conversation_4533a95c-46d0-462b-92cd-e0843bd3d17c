package com.zxy.product.system.entity;

import com.zxy.product.system.jooq.tables.pojos.PointAdjustmentEntity;

public class PointAdjustment extends PointAdjustmentEntity {

    public static final String TEMPLATE_INDEX = "序号";
    public static final String TEMPLATE_MEMBER_NAME_CN = "账号(必填)";
    public static final String TEMPLATE_POINT = "分值(必填，减分可输入 -9999.99 - -0.01的负数,增分可输入 0.01 - 9999.99 的正数)";
    public static final String TEMPLATE_REMARK = "说明";

    public static final Integer TEMPLATE_0 = 0;
    public static final Integer TEMPLATE_1 = 1;
    public static final Integer TEMPLATE_2 = 2;
    public static final Integer TEMPLATE_3 = 3;
    public static final Integer TEMPLATE_4 = 4;

    public static final Integer ADJUSTMENT_TYPE_1 = 1; //增加
    public static final Integer ADJUSTMENT_TYPE_2 = 2; //减少

    public static final String POINT_ADJUST = "point_adjust";


    //操作人
    private String operate;

    //被调整人
    private String adjustmentName;

    //被调整人
    private String adjustmentFullName;

    //被调整人
    private String pointString;

    public String getOperate() {
        return operate;
    }

    public void setOperate(String operate) {
        this.operate = operate;
    }

    public String getAdjustmentName() {
        return adjustmentName;
    }

    public void setAdjustmentName(String adjustmentName) {
        this.adjustmentName = adjustmentName;
    }

    public String getAdjustmentFullName() {
        return adjustmentFullName;
    }

    public void setAdjustmentFullName(String adjustmentFullName) {
        this.adjustmentFullName = adjustmentFullName;
    }

    public String getPointString() {
        return pointString;
    }

    public void setPointString(String pointString) {
        this.pointString = pointString;
    }
}
