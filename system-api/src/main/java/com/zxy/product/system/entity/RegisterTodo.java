package com.zxy.product.system.entity;

import com.zxy.product.system.jooq.tables.pojos.RegisterTodoEntity;

public class RegisterTodo extends RegisterTodoEntity {
    private static final long serialVersionUID = -7477130764243963376L;

    public static final Integer STATUS_PAAS = 1; //已处理

    public static final Integer STATUS_TO_DO = 0; //待处理

    private Integer approveStatus;
    private Long approveTime;

    public Integer getApproveStatus() {
        return approveStatus;
    }

    public void setApproveStatus(Integer approveStatus) {
        this.approveStatus = approveStatus;
    }

    public Long getApproveTime() {
        return approveTime;
    }

    public void setApproveTime(Long approveTime) {
        this.approveTime = approveTime;
    }
}
