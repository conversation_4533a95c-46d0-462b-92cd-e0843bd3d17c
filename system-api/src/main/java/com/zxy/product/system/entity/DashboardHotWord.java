package com.zxy.product.system.entity;

import com.zxy.product.system.jooq.tables.pojos.DashboardHotWordEntity;

/**
 * @Classname DashboardHotWord
 * @Description TODO
 * @Date 2022/3/15 1:46 下午
 * @Created by ykn
 */
public class DashboardHotWord extends DashboardHotWordEntity {

    private static final long serialVersionUID = 4047805264084558314L;
    /**
     *  热词类型
     *  1：自动
     *  2：人工
     */
    public static final Integer TYPE_AUTOMATIC =1;
    public static final Integer TYPE_ARTIFICIAL =2;

    /**
     *  根据当前业务 计算得出 表最大数量为120
     */
    public static final  int ALL_HOT_WORDS_SIZE=120;

    /**
     * 状态 显示0，隐藏1
     */
    public static final int STATUS_SHOW         = 0;
    public static final int STATUS_HIDE         = 1;

}
