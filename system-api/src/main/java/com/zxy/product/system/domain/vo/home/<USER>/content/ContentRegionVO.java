package com.zxy.product.system.domain.vo.home.featured.content;

import java.io.Serializable;

/**
 * 精选内容特殊处理相关
 * <AUTHOR>
 * @date 2025年02月25日 9:52
 */
public class ContentRegionVO implements Serializable {
    private static final long serialVersionUID = 8358772425969055451L;

    /**标识主键*/
    private String id;

    /**模块Id*/
    private String name;

    /**模块Code*/
    private String moduleCode;

    /**模块regionCode*/
    private String regionCode;

    /**模块排序*/
    private Integer sort;

    /**模块数据源*/
    private Integer dataSource;

    /**模块是否启用首页预览*/
    private Integer enableHomeBrowse;

    /**模块样式*/
    private String style;

    /**模块客户端*/
    private Integer clientType;

    public String getId() { return id; }

    public void setId(String id) { this.id = id; }

    public String getName() { return name; }

    public void setName(String name) { this.name = name; }

    public String getModuleCode() { return moduleCode; }

    public void setModuleCode(String moduleCode) { this.moduleCode = moduleCode; }

    public String getRegionCode() { return regionCode; }

    public void setRegionCode(String regionCode) { this.regionCode = regionCode; }

    public Integer getSort() { return sort; }

    public void setSort(Integer sort) { this.sort = sort; }

    public Integer getDataSource() { return dataSource; }

    public void setDataSource(Integer dataSource) { this.dataSource = dataSource; }

    public Integer getEnableHomeBrowse() { return enableHomeBrowse; }

    public void setEnableHomeBrowse(Integer enableHomeBrowse) { this.enableHomeBrowse = enableHomeBrowse; }

    public String getStyle() { return style; }

    public void setStyle(String style) { this.style = style; }

    public Integer getClientType() { return clientType; }

    public void setClientType(Integer clientType) { this.clientType = clientType; }

    @Override
    public String toString() {
        return "ContentRegionVO{" +
                "id='" + id + '\'' +
                ", name='" + name + '\'' +
                ", moduleCode='" + moduleCode + '\'' +
                ", regionCode='" + regionCode + '\'' +
                ", sort=" + sort +
                ", dataSource=" + dataSource +
                ", enableHomeBrowse=" + enableHomeBrowse +
                ", style='" + style + '\'' +
                ", clientType=" + clientType +
                '}';
    }
}
