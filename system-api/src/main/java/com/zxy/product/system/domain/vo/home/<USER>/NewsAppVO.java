package com.zxy.product.system.domain.vo.home.news;

import java.io.Serializable;

/**
 * 首页新闻资讯：覆写App回显VO
 * <AUTHOR>
 * @date 2024年11月25日 11:13
 */
public class NewsAppVO extends NewsParentVO implements Serializable {
    private static final long serialVersionUID = -5643024621073696132L;

    /**新闻资讯：配置Id*/
    private String id;

    /**新闻资讯：创建时间*/
    private Long createTime;

    /**新闻资讯：简介*/
    private String summary;

    /**新闻资讯：标题*/
    private String title;

    /**新闻资讯：浏览数*/
    private Integer visitors;

    public String getId() { return id; }

    public void setId(String id) { this.id = id; }

    public Long getCreateTime() { return createTime; }

    public void setCreateTime(Long createTime) { this.createTime = createTime; }

    public String getSummary() { return summary; }

    public void setSummary(String summary) { this.summary = summary; }

    public String getTitle() { return title; }

    public void setTitle(String title) { this.title = title; }

    public Integer getVisitors() { return visitors; }

    public void setVisitors(Integer visitors) { this.visitors = visitors; }

    @Override
    public String toString() {
        return "NewsAppVO{" +
                "id='" + id + '\'' +
                ", createTime=" + createTime +
                ", summary='" + summary + '\'' +
                ", title='" + title + '\'' +
                ", visitors=" + visitors +
                '}';
    }
}
