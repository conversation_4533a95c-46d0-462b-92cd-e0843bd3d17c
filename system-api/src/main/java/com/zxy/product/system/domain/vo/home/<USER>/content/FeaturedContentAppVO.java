package com.zxy.product.system.domain.vo.home.featured.content;

import java.io.Serializable;

/**
 * 首页精选内容：App回显VO
 * <AUTHOR>
 * @date 2024年11月25日 15:45
 */
public class FeaturedContentAppVO extends FeaturedContentParentVO implements Serializable {
    private static final long serialVersionUID = 7014132679882784367L;

    /**精选内容：配置Id*/
    private String id;

    /**精选内容：精选理由*/
    private String dataExt;

    /**精选内容：数据Id*/
    private String dataId;

    /**精选内容：数据名称*/
    private String dataName;

    /**精选内容：数据类型*/
    private Integer dataType;

    /**精选内容：数据URL跳转地址*/
    private String url;

    /**精选内容：数据图片地址，若存在直接赋值coverPath，否则取coverIdPath*/
    private String imagePath;

    /**精选内容：图片地址*/
    private String coverPath;

    public String getId() { return id; }

    public void setId(String id) { this.id = id; }

    public String getDataExt() { return dataExt; }

    public void setDataExt(String dataExt) { this.dataExt = dataExt; }

    public String getDataId() { return dataId; }

    public void setDataId(String dataId) { this.dataId = dataId; }

    public String getDataName() { return dataName; }

    public void setDataName(String dataName) { this.dataName = dataName; }

    public Integer getDataType() { return dataType; }

    public void setDataType(Integer dataType) { this.dataType = dataType; }

    public String getUrl() { return url; }

    public void setUrl(String url) { this.url = url; }

    public String getImagePath() { return imagePath; }

    public void setImagePath(String imagePath) { this.imagePath = imagePath; }

    public String getCoverPath() { return coverPath; }

    public void setCoverPath(String coverPath) { this.coverPath = coverPath; }

    @Override
    public String toString() {
        return "FeaturedContentAppVO{" +
                "id='" + id + '\'' +
                ", dataExt='" + dataExt + '\'' +
                ", dataId='" + dataId + '\'' +
                ", dataName='" + dataName + '\'' +
                ", dataType=" + dataType +
                ", url='" + url + '\'' +
                ", imagePath='" + imagePath + '\'' +
                ", coverPath='" + coverPath + '\'' +
                '}';
    }
}
