package com.zxy.product.system.entity;

import com.zxy.product.system.jooq.tables.pojos.PagePlanConfigEntity;

import java.util.List;

/**
 * @Description: 页面方案配置表
 * @Author: liuc
 * @CreateDate: 2020/9/10 14:56
 * @UpdateRemark: 修改内容
 * @Version: 1.0
 */
public class PagePlanConfig extends PagePlanConfigEntity {

    private static final long serialVersionUID = -1292364682280626552L;
    public static final Integer STATUS_DISABLED = 0;
    public static final Integer STATUS_ENABLED = 1;

    public static final Integer PAGE_INIT_OK = 2;

    public static final String URI = "system/page-config";

    public static final String PAGE_PLAN_CONFIG_NAME_COPY = "【复制】";

    public static final Integer CATEGORY_SAAS = 1;
    public static final Integer CATEGORY_PAAS = 2;

    /**
     * 方案关联页面配置
     */
    private List<PageRelation> pageRelation;

    private String organizationName;

    private String updateMemberName;

    public String getUpdateMemberName() {
        return updateMemberName;
    }

    public void setUpdateMemberName(String updateMemberName) {
        this.updateMemberName = updateMemberName;
    }

    public String getOrganizationName() {
        return organizationName;
    }

    public void setOrganizationName(String organizationName) {
        this.organizationName = organizationName;
    }

    public List<PageRelation> getPageRelation() {
        return pageRelation;
    }

    public void setPageRelation(List<PageRelation> pageRelation) {
        this.pageRelation = pageRelation;
    }
}
