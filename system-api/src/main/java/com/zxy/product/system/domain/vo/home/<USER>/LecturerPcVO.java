package com.zxy.product.system.domain.vo.home.Lecturer;

import java.io.Serializable;

/**
 * 首页讲师榜：覆写PC回显VO
 * <AUTHOR>
 * @date 2024年12月02日 14:26
 */
public class LecturerPcVO extends LecturerParentVO implements Serializable {
    private static final long serialVersionUID = 4837430426073934275L;

    /**讲师榜：讲师Id*/
    private String lectureId;

    /**讲师榜：讲师VO*/
    private LecturerVO lecturer;

    public String getLectureId() { return lectureId; }

    public void setLectureId(String lectureId) { this.lectureId = lectureId; }

    public LecturerVO getLecturer() { return lecturer; }

    public void setLecturer(LecturerVO lecturer) { this.lecturer = lecturer; }

    @Override
    public String toString() {
        return "LecturerPcVO{" +
                "lectureId='" + lectureId + '\'' +
                ", lecturer=" + lecturer +
                '}';
    }

    /**讲师榜：内置VO*/
    public static class LecturerVO implements Serializable{
        private static final long serialVersionUID = 1187676952076642658L;

        /**讲师榜：内置属性*/
        private String attributeId;

        /**讲师榜：图片地址*/
        private String coverPath;

        /**讲师榜：机构 cooperationType＝1有效'*/
        private String institutions;

        /**讲师榜：职务|职称*/
        private String jobName;

        /**讲师榜：级别名称*/
        private String levelName;

        /**讲师榜：讲师姓名*/
        private String name;

        /**讲师榜：类型 0 内部讲师  1 外部讲师*/
        private Integer type;

        /**讲师榜：单位*/
        private String unit;

        public String getAttributeId() { return attributeId; }

        public void setAttributeId(String attributeId) { this.attributeId = attributeId; }

        public String getCoverPath() { return coverPath; }

        public void setCoverPath(String coverPath) { this.coverPath = coverPath; }

        public String getInstitutions() { return institutions; }

        public void setInstitutions(String institutions) { this.institutions = institutions; }

        public String getJobName() { return jobName; }

        public void setJobName(String jobName) { this.jobName = jobName; }

        public String getLevelName() { return levelName; }

        public void setLevelName(String levelName) { this.levelName = levelName; }

        public String getName() { return name; }

        public void setName(String name) { this.name = name; }

        public Integer getType() { return type; }

        public void setType(Integer type) { this.type = type; }

        public String getUnit() { return unit; }

        public void setUnit(String unit) { this.unit = unit; }

        @Override
        public String toString() {
            return "LecturerVO{" +
                    "attributeId='" + attributeId + '\'' +
                    ", coverPath='" + coverPath + '\'' +
                    ", institutions='" + institutions + '\'' +
                    ", jobName='" + jobName + '\'' +
                    ", levelName='" + levelName + '\'' +
                    ", name='" + name + '\'' +
                    ", type=" + type +
                    ", unit='" + unit + '\'' +
                    '}';
        }
    }
}
