package com.zxy.product.system.entity;

import com.zxy.product.system.domain.vo.HomeParentVO;
import com.zxy.product.system.jooq.tables.pojos.HomeAdvertisementEntity;

import java.io.Serializable;

/**
 * Created by <PERSON><PERSON><PERSON> on 17/2/22.
 */
public class HomeAdvertisement extends HomeAdvertisementEntity {

    private static final long serialVersionUID = -3025274066527700835L;

    public static final int STATE_DRAFT = 0;
    public static final int STATE_ENABLE  = 1;

    public static final int CLIENT_TYPE_ALL = 0;
    public static final int CLIENT_TYPE_PC  = 1;
    public static final int CLIENT_TYPE_APP  = 2;
    public static final int UPDATE = 0;
    public static final int DELETE = -1;

    /**
     * 首页配置块：Banner子VO
     * <AUTHOR>
     * @date 2024年08月02日 9:22
     */
    public static class BannerChild extends HomeParentVO implements Serializable{
        private static final long serialVersionUID = -2350354958127470490L;

        /**首页配置块：Banner主键*/
        private String id;

        /**首页配置块：Banner标题*/
        private String title;

        /**首页配置块：Banner业务Id*/
        private String businessId;

        /**首页配置块：Banner的PC图片地址*/
        private String pcImagePath;

        /**首页配置块：Banner业务类型*/
        private Integer businessType;

        /**首页配置块：Banner的URL链接地址*/
        private String linkAddress;

        /**首页配置块：Banner的URL链接类型*/
        private Integer linkType;

        /**首页配置块：App图片地址*/
        private String appImagePath;

        public String getId() { return id; }

        public void setId(String id) { this.id = id; }

        public String getTitle() { return title; }

        public void setTitle(String title) { this.title = title; }

        public String getBusinessId() { return businessId; }

        public void setBusinessId(String businessId) { this.businessId = businessId; }

        public String getPcImagePath() { return pcImagePath; }

        public void setPcImagePath(String pcImagePath) { this.pcImagePath = pcImagePath; }

        public Integer getBusinessType() { return businessType; }

        public void setBusinessType(Integer businessType) { this.businessType = businessType; }

        public String getLinkAddress() { return linkAddress; }

        public void setLinkAddress(String linkAddress) { this.linkAddress = linkAddress; }

        public Integer getLinkType() { return linkType; }

        public void setLinkType(Integer linkType) { this.linkType = linkType; }

        public String getAppImagePath() { return appImagePath; }

        public void setAppImagePath(String appImagePath) { this.appImagePath = appImagePath; }

        @Override
        public String toString() {
            return "BannerChild{" +
                    "id='" + id + '\'' +
                    ", title='" + title + '\'' +
                    ", businessId='" + businessId + '\'' +
                    ", pcImagePath='" + pcImagePath + '\'' +
                    ", businessType=" + businessType +
                    ", linkAddress='" + linkAddress + '\'' +
                    ", linkType=" + linkType +
                    ", appImagePath='" + appImagePath + '\'' +
                    '}';
        }
    }


}
