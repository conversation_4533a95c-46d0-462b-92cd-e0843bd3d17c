package com.zxy.product.system.entity;

import com.zxy.product.system.jooq.tables.pojos.TopicEntity;

import java.util.List;

/**
 * <AUTHOR>
 * 话题
 *
 */
public class Topic extends TopicEntity {

	private static final long serialVersionUID = 5420550096864512894L;

	public static final String POSITION_CONTENT_TOPIC = "岗位内容";

	public static final Integer DELETE_FAlSE = 0;    //删除状态：未删除
    public static final Integer DELETE_TRUE = 1;  //删除状态，已删除
    public static final Integer STATUS_ENABLE = 1;    //启用
    public static final Integer STATUS_DISABLE = 0;  //禁用
	public static final Integer GROUP_TEMP = 0; //临时
	public static final Integer GROUP_STANDARD = 1; //标准
	public static final Integer RECOMMEND_YES = 1; // 首次登陆推荐-是
	public static final Integer RECOMMEND_NO = 0; // 首次登陆推荐-否
	public static final int TYPE_ALL = 0;// 话题排行榜汇总
	public static final int TYPE_YEAR = 1;// 话题排行榜年度排行
	public static final int TYPE_MONTH = 2;// 话题排行榜月度排行
	public static final int IS_BAR_TOPIC_YES = 1;	// 是否为问吧话题-是
	public static final int IS_BAR_TOPIC_NO = 0;	// 是否为问吧话题-否
    public static final int TOPIC_LEVEL_ZERO=0; //话题分级 1级
    public static final int TOPIC_LEVEL_ONE=1; //话题分级 1级
    public static final int TOPIC_LEVEL_TWO=2; //话题分级 2级
    public static final int TOPIC_LEVEL_THREE=3;//话题分级 3级
    public static final int INTELLECT_FLAG_TRUE=1;//是智能标签
    public static final int INTELLECT_FLAG_FALSE=0;//不是智能标签
    public static final int FRONT_IS_DISPLAYED_TURE=1;//不是智能标签
    public static final int FRONT_IS_DISPLAYED_FALSE=0;//不是智能标签
    public static final int FRONT_IS_DISPLAYED_ON = 1;//学员端展示
    public static final int FRONT_IS_DISPLAYED_OFF = 0;//学员端不展示

	private TopicType topicType;
	private List<TopicManager> topicManagers;
	private List<TopicExpert> topicExperts;
	private List<TopicObject> topicObjects;

	private List<TopicRelation> synonymsTopics;
	private List<TopicRelation> relationTopics;

	private String pname;
	private Integer sequence;

	private String typeName;

	private Boolean isParent;//是否为父标签
    private List<TopicStatistics> topicStatistics;

    private boolean defaultTopic;   //默认选中标签

    private List<Topic> subTopics;  //子标签

    private boolean contentSub;   //是否为岗位内容下的标签

    private String attentionId;    //关注ID  t_attention

    private long relatedCount;       //课程关联数

    private List<String> relatedTopicIds;   //关联词标签ID

    public List<String> getRelatedTopicIds() {
        return relatedTopicIds;
    }

    public void setRelatedTopicIds(List<String> relatedTopicIds) {
        this.relatedTopicIds = relatedTopicIds;
    }

    public long getRelatedCount() {
        return relatedCount;
    }

    public void setRelatedCount(long relatedCount) {
        this.relatedCount = relatedCount;
    }

    public String getAttentionId() {
        return attentionId;
    }

    public void setAttentionId(String attentionId) {
        this.attentionId = attentionId;
    }

    public boolean isDefaultTopic() {
        return defaultTopic;
    }

    public void setDefaultTopic(boolean defaultTopic) {
        this.defaultTopic = defaultTopic;
    }

    public List<Topic> getSubTopics() {
        return subTopics;
    }

    public void setSubTopics(List<Topic> subTopics) {
        this.subTopics = subTopics;
    }

    public boolean isContentSub() {
        return contentSub;
    }

    public void setContentSub(boolean contentSub) {
        this.contentSub = contentSub;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public Integer getSequence() {
        return sequence;
    }

    public void setSequence(Integer sequence) {
        this.sequence = sequence;
    }

    public String getPname() {
        return pname;
    }

    public void setPname(String pname) {
        this.pname = pname;
    }

    public List<TopicObject> getTopicObjects() {
		return topicObjects;
	}

	public void setTopicObjects(List<TopicObject> topicObjects) {
		this.topicObjects = topicObjects;
	}

	public TopicType getTopicType() {
		return topicType;
	}

	public void setTopicType(TopicType topicType) {
		this.topicType = topicType;
	}

    public List<TopicManager> getTopicManagers() {
        return topicManagers;
    }

    public void setTopicManagers(List<TopicManager> topicManagers) {
        this.topicManagers = topicManagers;
    }

    public List<TopicExpert> getTopicExperts() {
        return topicExperts;
    }

    public void setTopicExperts(List<TopicExpert> topicExperts) {
        this.topicExperts = topicExperts;
    }

    public Boolean getIsParent() {
        return isParent;
    }

    public void setIsParent(Boolean isParent) {
        this.isParent = isParent;
    }

    public List<TopicStatistics> getTopicStatistics() {
        return topicStatistics;
    }

    public void setTopicStatistics(List<TopicStatistics> topicStatistics) {
        this.topicStatistics = topicStatistics;
    }

    public List<TopicRelation> getSynonymsTopics() {
        return synonymsTopics;
    }

    public void setSynonymsTopics(List<TopicRelation> synonymsTopics) {
        this.synonymsTopics = synonymsTopics;
    }

    public List<TopicRelation> getRelationTopics() {
        return relationTopics;
    }

    public void setRelationTopics(List<TopicRelation> relationTopics) {
        this.relationTopics = relationTopics;
    }
}
