package com.zxy.product.system.entity;

import com.zxy.product.system.jooq.tables.pojos.TopicObjectEntity;

/**
 * <AUTHOR>
 * 话题与内容关联（人、课程、活动、知识、专题等）
 *
 */
public class TopicObject extends TopicObjectEntity {

	private static final long serialVersionUID = 9084391460497918454L;

	//关联类型（1 人关注  2 课程  3 专题  4 知识  5 直播  6 考试 7 调研  8 问题 9 专家 10 MOOC 11 专家工作室)
	public static final int TYPE_MEMBER = 1;
	public static final int TYPE_COURSE = 2;
	public static final int TYPE_SUBJECT = 3;
	public static final int TYPE_KNOWLEAGE = 4;
	public static final int TYPE_LIVE = 5;
	public static final int TYPE_EXAM = 6;
	public static final int TYPE_RESEARCH = 7;
	public static final int TYPE_QUESTION = 8;
	public static final int TYPE_EXPERT = 9;
	public static final int TYPE_MOOC = 10;
	public static final int TYPE_STUDIO = 11;

}
