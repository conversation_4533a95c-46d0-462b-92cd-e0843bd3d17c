package com.zxy.product.system.domain.vo.home.mini.banner;

import java.io.Serializable;

/**
 * 首页小Banner：覆写PC回显VO
 * <AUTHOR>
 * @date 2024年11月25日 11:33
 */
public class MiniBannerPcVO extends MiniBannerParentVO implements Serializable {
    private static final long serialVersionUID = 5839295862483776909L;

    /**小BANNER；配置Id*/
    private String id;

    /**小BANNER：业务Id*/
    private String businessId;

    /**小BANNER：业务类型*/
    private Integer businessType;

    /**小BANNER：URL链接地址*/
    private String linkAddress;

    /**小BANNER：URL类型*/
    private Integer linkType;

    /**小BANNER：PC图片地址*/
    private String pcImagePath;

    /**小BANNER：标题*/
    private String title;

    /**小BANNER：内容*/
    private String content;

    public String getId() { return id; }

    public void setId(String id) { this.id = id; }

    public String getBusinessId() { return businessId; }

    public void setBusinessId(String businessId) { this.businessId = businessId; }

    public Integer getBusinessType() { return businessType; }

    public void setBusinessType(Integer businessType) { this.businessType = businessType; }

    public String getLinkAddress() { return linkAddress; }

    public void setLinkAddress(String linkAddress) { this.linkAddress = linkAddress; }

    public Integer getLinkType() { return linkType; }

    public void setLinkType(Integer linkType) { this.linkType = linkType; }

    public String getPcImagePath() { return pcImagePath; }

    public void setPcImagePath(String pcImagePath) { this.pcImagePath = pcImagePath; }

    public String getTitle() { return title; }

    public void setTitle(String title) { this.title = title; }

    public String getContent() { return content; }

    public void setContent(String content) { this.content = content; }

    @Override
    public String toString() {
        return "MiniBannerPcVO{" +
                "businessId='" + businessId + '\'' +
                ", businessType=" + businessType +
                ", linkAddress='" + linkAddress + '\'' +
                ", linkType=" + linkType +
                ", pcImagePath='" + pcImagePath + '\'' +
                ", title='" + title + '\'' +
                ", content='" + content + '\'' +
                '}';
    }
}
