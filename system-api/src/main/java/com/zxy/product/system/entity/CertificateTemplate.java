package com.zxy.product.system.entity;

import java.util.List;

import com.zxy.product.system.jooq.tables.pojos.CertificateTemplateEntity;

/**
 * Created by <PERSON> on 2016/12/26.
 */
public class CertificateTemplate extends CertificateTemplateEntity {

    /**
     *
     */
    private static final long serialVersionUID = 832423480862545907L;
    private Organization organization;
    private List<CertificateConfig> configList;

    public static final String URI = "operation/certificate";

    public static final Integer IS_DEFAULT = 1;
    public static final Integer NOT_DEFAULT = 0;
    /**
     * 普通考试证书（横版）
     */
    public static final Integer TYPE_EXAM_TRANSVERSE = 16;

    public Organization getOrganization() {
        return organization;
    }

    public void setOrganization(Organization organization) {
        this.organization = organization;
    }

    public List<CertificateConfig> getConfigList() {
        return configList;
    }

    public void setConfigList(List<CertificateConfig> configList) {
        this.configList = configList;
    }
}
