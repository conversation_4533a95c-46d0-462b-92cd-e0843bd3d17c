package com.zxy.product.system.content;

/**
 * 公共开关枚举
 *
 * <AUTHOR>
 * @date 2024年03月25日 11:19
 */
public enum SwitchEnum {
    /**行为数据采集开关,开关开启,不保存*/
    BehaviorAcquisition("internalSwitch-BehaviorAcquisition",0),
    /**相关课程开关-数据缓存key:   internalSwitch-relatedCoursesdata,数据初始化在course中的t_business_emergency表里*/
    RelatedCourses("internalSwitch-relatedCourses",1),
    /**热词开关,开启只查询人工热词,数据缓存key: internalSwitch-hotWord-data*/
    HotWord("internalSwitch-hotWord",10),
    /**活动页直播,开关开启,不查询*/
    LivePage("internalSwitch-LivePage",11),
    /**热门内容,数据初始化在course中的t_business_emergency表里*/
    HotContent("internalSwitch-hotContent",12),
    /**热议,开关开启查询默认数据,数据保存在report中的t_intelligence_emergency_config表中*/
    DiscoveryPageIsHotTopic("internalSwitch-DiscoveryPageIsHotTopic",13),
    /**回原隐藏*/
    DataBackSource("internalSwitch-dataBackSource",15),
    /**课程/专题列表切换*/
    CourseAndSubjectSwitch("internalSwitch-courseAndSubjectSwitch",16),
    /**白天不允许发布/时间使用逗号隔开,7,19*/
    PublicationRestriction("internalSwitch-PublicationRestriction",17),
    /**个人画像,开关开启隐藏*/
    PersonalPortrait("internalSwitch-personalPortrait",18),
    /**相关专题开关-数据缓存key:   internalSwitch-relatedSubjectdata,数据初始化在course中的t_business_emergency表里*/
    RelatedSubject("internalSwitch-relatedSubject",2),
    /**课程Or专题排序开关*/
    CourseAndSubjectOrder("internalSwitch-courseAndSubjectOrder",20),
    /**智能搜索开关,开启则兜底返回null*/
    IntelligentSearch("internalSwitch-intelligentSearch",3),
    /**智能推荐开关,,数据初始化在course中的t_business_emergency表里*/
    IntelligentRecommendation("internalSwitch-intelligentRecommendation",4),
    /**个人中心,推荐,开关开启,隐藏模块*/
    GuessYouLike("internalSwitch-guessYouLike",5),
    /**学习卡片开关*/
    StudyCard("internalSwitch-studyCard",8),
    /**智能客服开关*/
    IntelligentAgentsSwitch("internalSwitch-IntelligentAgentsSwitch",21),

    LearningStatus("internalSwitch-learningStatus",22),

    /** 讨论区仅展示审核通过的内容 */
    OnlyAuditContent("internalSwitch-OnlyAuditContent",23),
    /**问吧首页排序开关*/
    AskBarHomeOrderSwitch("internalSwitch-AskBarHomeOrderSwitch",24),
    /**
     * 学习计划推送开关
     */
    LearningPlanPush("internalSwitch-learningPlanPush",25),

    /**限流限频开关*/
    FlowLimitingSwitch( "internalSwitch-FlowLimitingSwitch", 26 )
    ;

    private final String key;
    private final Integer type;

    SwitchEnum(String key, Integer type) {
        this.key = key;
        this.type=type;
    }

    public String getKey() { return key; }

    public Integer getType() { return type; }
}
