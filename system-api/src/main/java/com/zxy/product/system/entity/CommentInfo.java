package com.zxy.product.system.entity;

import com.zxy.product.system.jooq.tables.pojos.CommentInfoEntity;

import java.util.List;

/**
 * <AUTHOR>
 *
 */
public class CommentInfo extends CommentInfoEntity {

    private static final long serialVersionUID = -7850316818639595310L;

    public static final Integer AUDIT_STATUS_WAIT = 0; // 未审核
    public static final Integer AUDIT_STATUS_PASS = 1; // 已通过
    public static final Integer AUDIT_STATUS_REFUSE = 2; // 已拒绝

    //红船审核状态 - 待审核
    public static final Integer RED_BOAT_AUDIT_STATUS_WAIT = 0;
    //红船审核状态 - 审核中
    public static final Integer RED_BOAT_AUDIT_STATUS_REVIEW = 1;
    //红船审核状态 - 通过审核
    public static final Integer RED_BOAT_AUDIT_STATUS_PASS = 2;
    //红船审核状态 - 未通过审核
    public static final Integer RED_BOAT_AUDIT_STATUS_REFUSE = 3;
    //红船审核状态 - 超时
    public static final Integer RED_BOAT_AUDIT_STATUS_PASS_TO = 4;

    //红船复核状态 - 待复核
    public static final Integer RED_BOAT_TO_REVIEW_W = 0;
    //红船复核状态 - 未采纳
    public static final Integer RED_BOAT_TO_REVIEW_N = 1;

    /**
     * 红船审核时长阈值 暂定0.5h
     */
    public static final Long RED_AUDIT_THRESHOLD = 1800000L;


    public static final Integer ACCUSE_STATUS_NO = 0; // 未被举报
    public static final Integer ACCUSE_STATUS_YES = 1; // 被举报

    public static final Integer TOP_STATUS_YES = 1; // 置顶
    public static final Integer TOP_STATUS_NO = 0; // 未置顶

    public static final Integer TOP_STATUS_HIDE = 1; // 隐藏
    public static final Integer TOP_STATUS_SHOW = 0; // 显示

    public static final Integer IS_SENSITIVE = 1; // 是敏感词
    public static final Integer IS_NOT_SENSITIVE = 0; // 不是敏感词

    public static final int BUSINESS_TYPE_COURSE = 1; // 课程
    public static final int BUSINESS_TYPE_SUBJECT = 2; // 专题
    public static final int BUSINESS_TYPE_KNOWLEDGE = 3;//知识
    public static final int BUSINESS_TYPE_CLASS = 10;//班级
    public static final int BUSINESS_TYPE_EXAM_NETWORK= 5;//网络部认证考试

    /**
     * 员工直通车问题类型:6 提议 7 留言 8 咨询
     */
    public static final int BUSINESS_TYPE_DIRECT_TRAIN_PROPOSE = 6;
    public static final int BUSINESS_TYPE_DIRECT_TRAIN_MESSAGE = 7;
    public static final int BUSINESS_TYPE_DIRECT_TRAIN_CONSULT = 8;

    /**
     * 专家工作室讨论
     */
    public static final int BUSINESS_TYPE_STUDIO_DISCUSS = 11;

    /**
     * 专家工作室直播
     */
    public static final int BUSINESS_TYPE_STUDIO_LIVE = 12;

    /**
     * 是否是专家工作室 0否 1是
     */
    public static final int IS_STUDIO = 1;

    public static final int DELETE_FLASE = 0; //删除状态：未删除
    public static final int DELETE_TRUE = 1; //删除状态，已删除

    private String avatarId; // 头像文件id
    private String memberName; // 用户名称
    private String praiseId; // 点赞id
    private Organization memberOrg;
    private String createTimeStr;//创建时间显示
    private String studyTeamName;// 团队名称
    private String objectId;//审核对象id

    private SpeechAudit speechAudit;

    public SpeechAudit getSpeechAudit() {
        return speechAudit;
    }

    public void setSpeechAudit(SpeechAudit speechAudit) {
        this.speechAudit = speechAudit;
    }

    public String getObjectId() {
        return objectId;
    }

    public void setObjectId(String objectId) {
        this.objectId = objectId;
    }

    private List<CommentReply> replies;

    public String getAvatarId() {
        return avatarId;
    }

    public void setAvatarId(String avatarId) {
        this.avatarId = avatarId;
    }

    public String getMemberName() {
        return memberName;
    }

    public void setMemberName(String memberName) {
        this.memberName = memberName;
    }

    public String getPraiseId() {
        return praiseId;
    }

    public void setPraiseId(String praiseId) {
        this.praiseId = praiseId;
    }

    public List<CommentReply> getReplies() {
        return replies;
    }

    public void setReplies(List<CommentReply> replies) {
        this.replies = replies;
    }

    public Organization getMemberOrg() {
        return memberOrg;
    }

    public void setMemberOrg(Organization memberOrg) {
        this.memberOrg = memberOrg;
    }

    public String getCreateTimeStr() {
        return createTimeStr;
    }

    public void setCreateTimeStr(String createTimeStr) {
        this.createTimeStr = createTimeStr;
    }

    public String getStudyTeamName() {
        return studyTeamName;
    }

    public void setStudyTeamName(String studyTeamName) {
        this.studyTeamName = studyTeamName;
    }
}
