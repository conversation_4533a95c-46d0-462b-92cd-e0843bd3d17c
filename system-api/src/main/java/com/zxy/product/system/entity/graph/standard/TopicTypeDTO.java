package com.zxy.product.system.entity.graph.standard;

import java.io.Serializable;

/**
 * 知识图谱——标准标签(根)DTO
 *
 * <AUTHOR>
 * @date 2023年11月09日 11:09
 */
public class TopicTypeDTO implements Serializable {
    private static final long serialVersionUID=1L;

    /**标签(根)Id*/
    private String topicTypeId;

    /**标签(根)名称*/
    private String topicTypeName;

    /**标签(根)描述*/
    private String topicDescription;

    /**状态*/
    private String status;

    /**标签性质*/
    private String group;


    public String getTopicTypeId() { return topicTypeId; }

    public void setTopicTypeId(String topicTypeId) { this.topicTypeId = topicTypeId; }

    public String getTopicTypeName() { return topicTypeName; }

    public void setTopicTypeName(String topicTypeName) { this.topicTypeName = topicTypeName; }

    public String getTopicDescription() { return topicDescription; }

    public void setTopicDescription(String topicDescription) { this.topicDescription = topicDescription; }

    public String getStatus() { return status; }

    public void setStatus(String status) { this.status = status; }

    public String getGroup() { return group; }

    public void setGroup(String group) { this.group = group; }

    @Override
    public String toString() {
        return "TopicTypeDTO{" +
                "topicTypeId='" + topicTypeId + '\'' +
                ", topicTypeName='" + topicTypeName + '\'' +
                ", topicDescription='" + topicDescription + '\'' +
                ", status='" + status + '\'' +
                '}';
    }
}
