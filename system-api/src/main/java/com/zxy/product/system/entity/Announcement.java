package com.zxy.product.system.entity;

import com.zxy.product.system.jooq.tables.pojos.AnnouncementEntity;

/**
 * 公告
 */
public class Announcement extends AnnouncementEntity{
    private static final long serialVersionUID = 3005358211598907729L;

    public static final String URI = "operation/announcement";
    private Member publishMember;
    private Organization organization;
    private AnnouncementDetail announcementDetail;
    //发布状态：1已发布 2草稿
    public static final int STATUS_PUBLISH = 1;
    public static final int STATUS_DRAFT = 2;

    public AnnouncementDetail getAnnouncementDetail() {
        return announcementDetail;
    }

    public void setAnnouncementDetail(AnnouncementDetail announcementDetail) {
        this.announcementDetail = announcementDetail;
    }

    public Organization getOrganization() {
        return organization;
    }

    public void setOrganization(Organization organization) {
        this.organization = organization;
    }

    public Member getPublishMember() {
        return publishMember;
    }

    public void setPublishMember(Member publishMember) {
        this.publishMember = publishMember;
    }
}
