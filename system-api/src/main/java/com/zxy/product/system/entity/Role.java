package com.zxy.product.system.entity;

import java.util.List;

import com.zxy.product.system.jooq.tables.pojos.RoleEntity;

/**
 * <AUTHOR>
 *
 */
public class Role extends RoleEntity {

    private static final long serialVersionUID = -7805959528740900341L;

    public static final Integer INIT_YES = 0; // 是系统默认角色
    public static final Integer INIT_NO = 1; // 不是系统默认角色
    public static final String URI = "system/role";
    /**
     * 默认角色
     */
    public static final Integer TYPE_STANDARD = 0;
    /**
     * 元角色
     */
    public static final Integer TYPE_ELEMENT = 1;
    public static final Integer CHILD_FLAG_NO = 0;
    public static final Integer CHILD_FLAG_YES = 1;

    private boolean owned;
    private String parentName;
    private String grantId;
    private Organization organization;
    private List<Menu> menus;
    private List<Organization> grantedOrganizations;
    private Integer memberCount;
    private String grantOrganizationName;
    private Grant grant;

    public Grant getGrant() {
        return grant;
    }

    public void setGrant(Grant grant) {
        this.grant = grant;
    }

    public String getGrantOrganizationName() {
        return grantOrganizationName;
    }

    public void setGrantOrganizationName(String grantOrganizationName) {
        this.grantOrganizationName = grantOrganizationName;
    }

    public Integer getMemberCount() {
        return memberCount;
    }

    public void setMemberCount(Integer memberCount) {
        this.memberCount = memberCount;
    }

    public String getGrantId() {
		return grantId;
	}

	public void setGrantId(String grantId) {
		this.grantId = grantId;
	}

	public boolean getOwned() {
		return owned;
	}

	public void setOwned(boolean owned) {
		this.owned = owned;
	}

	public List<Menu> getMenus() {
        return menus;
    }

    public String getParentName() {
        return parentName;
    }

    public void setParentName(String parentName) {
        this.parentName = parentName;
    }

    public void setMenus(List<Menu> menus) {
        this.menus = menus;
    }

    public Organization getOrganization() {
        return organization;
    }

    public void setOrganization(Organization organization) {
        this.organization = organization;
    }

    public List<Organization> getGrantedOrganizations() {
        return grantedOrganizations;
    }

    public void setGrantedOrganizations(List<Organization> grantedOrganizations) {
        this.grantedOrganizations = grantedOrganizations;
    }

}
