package com.zxy.product.system.entity;

import com.zxy.product.system.jooq.tables.pojos.HomeInteractiveCommunicationEntity;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025年07月14日 14:50
 * @description 首页互动交流配置表
 */
public class HomeInteractiveCommunication extends HomeInteractiveCommunicationEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    // 问吧数据字段 - 参考 /find-by-ids 接口返回的字段
    private String title;           // 问题标题
    private Integer type;           // 问题类型
    private String createMemberId;  // 创建人ID
    private Long questionCreateTime; // 问题创建时间
    private String createMemberFullName; // 创建人姓名

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getCreateMemberId() {
        return createMemberId;
    }

    public void setCreateMemberId(String createMemberId) {
        this.createMemberId = createMemberId;
    }

    public Long getQuestionCreateTime() {
        return questionCreateTime;
    }

    public void setQuestionCreateTime(Long questionCreateTime) {
        this.questionCreateTime = questionCreateTime;
    }

    public String getCreateMemberFullName() {
        return createMemberFullName;
    }

    public void setCreateMemberFullName(String createMemberFullName) {
        this.createMemberFullName = createMemberFullName;
    }
}
