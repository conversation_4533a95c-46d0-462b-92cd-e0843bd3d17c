package com.zxy.product.system.entity;

import com.zxy.product.system.jooq.tables.pojos.TopicTypeEntity;
/**
 *
 * <AUTHOR>
 *
 */
public class TopicType extends TopicTypeEntity{


	private static final long serialVersionUID = 5960064362296427828L;

	public static final int DELETE_FLASE = 0;    //删除状态：未删除
    public static final int DELETE_TRUE = 1;  //删除状态，已删除

    public static final int STATUS_ENABLE = 1;    //启用
    public static final int STATUS_DISABLE = 0;  //禁用

    public static final int TOPIC_TYPE_ADD = 11;
    public static final int TOPIC_TYPE_UPDATE = 22;
    public static final String TYPE_SMART_LABEL_CODE = "znbq";

}
