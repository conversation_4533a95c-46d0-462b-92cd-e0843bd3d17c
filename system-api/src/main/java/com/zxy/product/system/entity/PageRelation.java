package com.zxy.product.system.entity;

import com.zxy.product.system.jooq.tables.pojos.PageRelationEntity;

import java.util.List;

/**
* @Description:    方案和页面关联表
* @Author:         liuc
* @CreateDate:     2020/9/10 14:56
* @UpdateRemark:   修改内容
* @Version:        1.0
*/
public class PageRelation extends PageRelationEntity {
    private static final long serialVersionUID = -1292364682280626552L;

    public static final Integer PC_TYPE = 1;
    public static final Integer APP_TYPE = 2;

    /**
     * 首页列表页类型
     */
    public static final Integer BUSINESS_TYPE_PAGE_LIST = 2;
    /**
     * 子首页类型
     */
    public static final Integer BUSINESS_TYPE_CHILD_PAGE = 3;


}
