package com.zxy.product.system.result;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date ：Created in 2021/6/28
 * @description ：弹幕方式
 */
public class Barrage implements Serializable {
    String preType;// 前缀
    String text;// 内容
    boolean select; //是否选择
    String typeface;//内容字体
    String size;//内容字号大小
    String color;//内容颜色

    public String getPreType() {
        return preType;
    }

    public void setPreType(String preType) {
        this.preType = preType;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public boolean isSelect() {
        return select;
    }

    public void setSelect(boolean select) {
        this.select = select;
    }

    public String getTypeface() {
        return typeface;
    }

    public void setTypeface(String typeface) {
        this.typeface = typeface;
    }

    public String getSize() {
        return size;
    }

    public void setSize(String size) {
        this.size = size;
    }

    public String getColor() {
        return color;
    }

    public void setColor(String color) {
        this.color = color;
    }
}
