package com.zxy.product.system.domain.dto;

import java.io.Serializable;

/**
 * 向上递归标签DTO
 *
 * <AUTHOR>
 * @date 2023年12月08日 1:54
 */
public class RecursionTopicDTO implements Serializable {
    private static final long serialVersionUID=1L;

    /**标签Id*/
    private String parentId;

    /**标签级别*/
    private String level;

    /**标签根Id*/
    private String typeId;

    public String getParentId() { return parentId; }

    public void setParentId(String parentId) { this.parentId = parentId; }

    public String getLevel() { return level; }

    public void setLevel(String level) { this.level = level; }

    public String getTypeId() { return typeId; }

    public void setTypeId(String typeId) { this.typeId = typeId; }

    @Override
    public String toString() {
        return "RecursionTopicDTO{" +
                "parentId='" + parentId + '\'' +
                ", level='" + level + '\'' +
                ", typeId='" + typeId + '\'' +
                '}';
    }
}
