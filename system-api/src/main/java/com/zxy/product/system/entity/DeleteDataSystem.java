package com.zxy.product.system.entity;

import com.zxy.product.system.jooq.tables.pojos.DeleteDataSystemEntity;

public class DeleteDataSystem extends DeleteDataSystemEntity {

    private static final String DATABASE_NAME = "system";

    public static final String ORGANIZATION = "t_organization";

    public static final String ORGANIZATION_DETAIL = "t_organization_detail";

    public static final String COMMENT_INFO = "t_comment_info";

    public static final String TOPIC = "t_topic";

    public static final String POINT_GRADE = "t_point_grade";

    public static DeleteDataSystem getDeleteData(String tableName, String businessId, String companyId){
        DeleteDataSystem deleteData = new DeleteDataSystem();
        deleteData.setDatabaseName(DATABASE_NAME);
        deleteData.setBusinessId(businessId);
        deleteData.setTableName(tableName);
        deleteData.setBusinessId(businessId);
        deleteData.setCompanyId(companyId);
        deleteData.forInsert();
        return deleteData;
    }

}
