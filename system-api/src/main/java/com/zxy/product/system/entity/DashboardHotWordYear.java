package com.zxy.product.system.entity;

import com.zxy.product.system.jooq.tables.pojos.DashboardHotWordEntity;
import com.zxy.product.system.jooq.tables.pojos.DashboardHotWordYearEntity;


public class DashboardHotWordYear extends DashboardHotWordYearEntity {

    private static final long serialVersionUID = -3583269820540488904L;

    /**
     *  热词类型
     *  1：自动
     *  2：人工
     */
    public static final Integer TYPE_AUTOMATIC =1;
    public static final Integer TYPE_ARTIFICIAL =2;

    /**
     *  根据当前业务 计算得出 表最大数量为120
     */
    public static final  int ALL_HOT_WORDS_SIZE=120;


}
