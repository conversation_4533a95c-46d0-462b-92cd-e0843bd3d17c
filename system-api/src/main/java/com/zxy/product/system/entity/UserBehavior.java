package com.zxy.product.system.entity;

import com.zxy.product.system.jooq.tables.pojos.UserBehaviorEntity;

public class UserBehavior extends UserBehaviorEntity {
    /**
     * type类型 1：收藏，2：取消收藏，3：关注，4：取消关注，5：点赞，6：取消点赞，7：下载，8：分享，9：点击，10：发现点击，11：进入页面。
     */
    public static final String COLLECT="1"; // 收藏
    public static final String COLLECT_CANCEL="2";//取消收藏
    public static final String ATTENTION="3";//关注
    public static final String ATTENTION_CANCEL="4";//取消关注
    public static final String PRAISE="5";//点赞
    public static final String PRAISE_CANCEL="6";//取消点赞
    public static final String DOWNLOAD = "7"; // 下载
    public static final String SHARE = "8"; // 分享
    public static final String CLICK="9";//点击
    public static final String DISCOVER_CLICK="10";//发现点击
    //public static final String ENTER_PAGE="11";//进入页面

    /**
     * 11:首页推荐每天的点击量  12:首页推荐每天的曝光量 13:每天进入发现页面的人数 14:每天首页点击人数 15:发现页面曝光
     */
    public static final String[] TYPES = {"11","12","13","14","15"};


    /**
     * contentType内容类型 1：专题，2：课程，3：知识，4：直播，5：话题，6：文章，7：问题，8：调研，9：考试，10：班级，11：培训班，12：为你推荐，13：猜你喜欢。
     */
    public static final String SUBJECT="1";//专题
    public static final String COURSE="2";//课程
    public static final String KNOWLEDGE="3";//知识
    public static final String GENSEE="4";//直播
    public static final String TOPIC="5";//话题
    public static final String ARTICLE="6";//文章
    public static final String QUESTION="7";//问题
    public static final String RESEARCH="8";//调研
    public static final String EXAM="9";//考试
    public static final String CLASS="10";//班级
    public static final String TRAIN="11";//培训班
    //public static final String RECOMMEND="12";//为你推荐
    //public static final String LOVE="13";//猜你喜欢

    /**
     * 客户端类型：0：PC；1：APP
     */
    public static final String CLIENT_TYPE_PC = "0"; // 分享
    public static final String CLIENT_TYPE_APP = "1"; // 分享

    /**
     * 状态
     */
    public static final String DELETE_FLASE = "0"; //状态：未删除
    public static final String DELETE_TRUE = "1"; //状态，已删除




}
