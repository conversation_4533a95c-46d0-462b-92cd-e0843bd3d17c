package com.zxy.product.system.result;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date ：Created in 2021/6/28
 * @description ：图片水印
 */
public class ImageWatermark implements Serializable {
    String file;// 图片路径
    String opacity;// 透明度
    boolean select;// 是否选择

    public String getFile() {
        return file;
    }

    public void setFile(String file) {
        this.file = file;
    }

    public String getOpacity() {
        return opacity;
    }

    public void setOpacity(String opacity) {
        this.opacity = opacity;
    }

    public boolean isSelect() {
        return select;
    }

    public void setSelect(boolean select) {
        this.select = select;
    }
}
