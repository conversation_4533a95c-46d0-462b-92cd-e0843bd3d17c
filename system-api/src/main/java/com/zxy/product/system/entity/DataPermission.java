package com.zxy.product.system.entity;

import com.zxy.product.system.jooq.tables.pojos.DataPermissionEntity;

/**
 * <AUTHOR>
 *
 */
public class DataPermission extends DataPermissionEntity{

    private static final long serialVersionUID = -7840848181943898633L;

    public static final String CODE_JOB_TYPE = "JOB_TYPE"; // 职务类别
    public static final String CODE_JOB = "JOB"; // 职务
    public static final String CODE_POSITION = "POSITION"; // 职位
    public static final String CODE_LECTURER = "LECTURER"; // 讲师
    public static final String CODE_CERTIFICATE = "CERTIFICATE"; // 证书
    public static final String CODE_TAG = "TAG"; // 群组
    public static final String CODE_COURSE = "COURSE"; // 课程
    public static final String CODE_KNOWLEDGE = "KNOWLEDGE"; // 知识

    public static final Integer LEVEL_CHECKED = 1; // 选中
    public static final Integer LEVEL_UNCHECKED = 0; // 取消选中

}
