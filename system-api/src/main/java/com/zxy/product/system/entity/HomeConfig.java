package com.zxy.product.system.entity;

import com.zxy.product.system.jooq.tables.pojos.HomeConfigEntity;

import java.util.List;

public class HomeConfig extends HomeConfigEntity{

    public static final Integer PLATFORM_APP = 2;
    public static final Integer PLATFORM_PC = 1;
    public static final Integer PLATFORM_PC_APP = 3;
    /**
     *
     */
    private static final long serialVersionUID = 4225978983125616383L;
    /**
     *
     * 状态
     * 0 草稿
     * 1 启用
     * 2 禁用
     */

    public static final int STATE_DRAFT = 0;
    public static final int STATE_ENABLE  = 1;
    public static final int STATE_DISABLE = 2;

    public static final int DELETE_FLAG_NO = 0;
    public static final int DELETE_FLAG_YES = 1;

    public static final int DISABLE_BROWSE = 0;
    public static final int ENABLE_BROWSE = 1;

    public static final String VERSION_OLD = "1.0";
    public static final String VERSION_NEW = "2.0";

    /**
     * 0=机构首页,1=虚拟空间
     */
    public static final int TYPE_INSTITUTIONS = 0;
    /**
     * 0=机构首页,1=虚拟空间
     */
    public static final int TYPE_VIRTUAL_SPACE = 1;


    /**
     * 子首页数据
     */
    private List<PageRelation> homePageJson;



    private String organizationName;

    public String getOrganizationName() {
        return organizationName;
    }

    public void setOrganizationName(String organizationName) {
        this.organizationName = organizationName;
    }

    private List<PageRelation> pageRelation;

    public List<PageRelation> getPageRelation() {
        return pageRelation;
    }

    public void setPageRelation(List<PageRelation> pageRelation) {
        this.pageRelation = pageRelation;
    }


    public List<PageRelation> getHomePageJson() {
        return homePageJson;
    }

    public void setHomePageJson(List<PageRelation> homePageJson) {
        this.homePageJson = homePageJson;
    }
}
