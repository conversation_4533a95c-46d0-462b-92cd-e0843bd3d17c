package com.zxy.product.system.content;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ProjectName: system
 * @Package: com.zxy.product.system.content
 * @ClassName: ProvinceEnum
 * @author: <PERSON>
 * @description: 省份枚举
 * @date: 2024/12/10 17:30
 * @version: 1.0
 */
public enum ProvinceEnum {

    ANHUI("安徽", 1),
    BEIJING("北京", 2),
    CHONGQING("重庆", 3),
    FUJIAN("福建", 4),
    GANSU("甘肃", 5),
    GUANGDONG("广东", 6),
    GUANGXI("广西壮族自治区", 7),
    GUIZHOU("贵州", 8),
    HAINAN("海南", 9),
    HEBEI("河北", 10),
    HENAN("河南", 11),
    HEILONGJIANG("黑龙江", 12),
    HUBEI("湖北", 13),
    HUNAN("湖南", 14),
    JIANGSU("江苏", 15),
    JIANGXI("江西", 16),
    JILIN("吉林", 17),
    LIAONING("辽宁", 18),
    INNER_MONGOLIA("内蒙古自治区", 19),
    NINGXIA("宁夏回族自治区", 20),
    QINGHAI("青海", 21),
    SHAANXI("陕西", 22),
    SHANDONG("山东", 23),
    SHANXI("山西", 24),
    SHANGHAI("上海", 25),
    SICHUAN("四川", 26),
    TAIWAN("台湾", 27),
    TIANJIN("天津", 28),
    TIBET("西藏自治区", 29),
    XINJIANG("新疆维吾尔自治区", 30),
    YUNNAN("云南", 31),
    ZHEJIANG("浙江", 32),
    HONG_KONG("香港特别行政区", 33),
    MACAU("澳门特别行政区", 34);

    private final String chineseName;
    private final int code;

    ProvinceEnum(String chineseName, int code) {
        this.chineseName = chineseName;
        this.code = code;
    }

    public String getChineseName() {
        return chineseName;
    }

    public int getCode() {
        return code;
    }

    @Override
    public String toString() {
        return code + " - " + chineseName;
    }

    /**
     * 通过code 获取名称
     *
     * @param code code
     * @return java.lang.String
     * @throws
     * @method getChineseName
     * <AUTHOR> Qian
     * @version 1.0
     * @date 2024/12/10 20:17
     */
    public static String getChineseName(int code) {
        for (ProvinceEnum value : ProvinceEnum.values()) {
            if (value.code == code) {
                return value.getChineseName();
            }
        }
        return null;
    }


    /**
     * 获取所有枚举
     *
     * @return
     */
    public static Map<Integer, String> getAllProvinces() {
        Map<Integer, String> result = new HashMap<>(36);
        for (ProvinceEnum value : ProvinceEnum.values()) {
            result.put(value.getCode(), value.getChineseName());
        }
        return result;
    }

    /**
     * 获取所有枚举
     *
     * @return
     */
    public static List<Map<Integer, String>> getAllProvinceList() {
        List<Map<Integer, String>> result =  new ArrayList<>();
        for (ProvinceEnum value : ProvinceEnum.values()) {
            Map<Integer, String> province = new HashMap<>();
            province.put(value.getCode(), value.getChineseName());
            result.add(province);
        }
        return result;
    }
}
