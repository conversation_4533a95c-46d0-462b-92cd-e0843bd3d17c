package com.zxy.product.system.entity;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @create 2023/12/13 10:53
 */

public class PointResultByMemberIdVo implements Serializable {

    /**
     * 余额
     */
    private Integer pointBalance;

    /**
     * 历史累计积分
     */
    private Integer pointHistory;

    /**
     * 积分等级名称
     */
    private String gradeName;

    /**
     * 积分等级图标url
     */
    private String cover;

    /**
     * 下一级还差的积分
     */
    private Integer nextGradePoint;

    /**
     * 下一级积分等级名称
     */
    private String nextGradeName;

    /**
     * 是否是第一级
     */
    private Boolean firstGrade;


    public Integer getPointBalance() {
        return pointBalance;
    }

    public void setPointBalance(Integer pointBalance) {
        this.pointBalance = pointBalance;
    }

    public Integer getPointHistory() {
        return pointHistory;
    }

    public void setPointHistory(Integer pointHistory) {
        this.pointHistory = pointHistory;
    }

    public String getGradeName() {
        return gradeName;
    }

    public void setGradeName(String gradeName) {
        this.gradeName = gradeName;
    }

    public String getCover() {
        return cover;
    }

    public void setCover(String cover) {
        this.cover = cover;
    }

    public Integer getNextGradePoint() {
        return nextGradePoint;
    }

    public void setNextGradePoint(Integer nextGradePoint) {
        this.nextGradePoint = nextGradePoint;
    }

    public String getNextGradeName() {
        return nextGradeName;
    }

    public void setNextGradeName(String nextGradeName) {
        this.nextGradeName = nextGradeName;
    }

    public Boolean getFirstGrade() {
        return firstGrade;
    }

    public void setFirstGrade(Boolean firstGrade) {
        this.firstGrade = firstGrade;
    }
}
