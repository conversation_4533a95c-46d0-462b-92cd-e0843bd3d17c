package com.zxy.product.system.entity;


import com.zxy.product.system.jooq.tables.pojos.CoverPatternEntity;

import java.io.Serializable;
import java.util.LinkedHashMap;

/**
 * 封面配置
 *
 * @Author: mutao
 * @Date: 2021/11/16 9:20
 */
public class CoverPattern extends CoverPatternEntity {

    /**
     * setting_json里面对字段的使能str
     */
    public static final String ENABLE_STR = "enable";
    /**
     * setting_json修改json
     */
    public static final String UPDATE_JSON_STR = "updateJson";

    private String sizeStr;
    private Integer width;
    private Integer height;
    private String showFieldLabels;
    private String updateMemberName;
    private LinkedHashMap<String,Object> settingJsonObj;
    private LinkedHashMap<String,String> checkedSettingJsonObj;

    public LinkedHashMap<String, String> getCheckedSettingJsonObj() {
        return checkedSettingJsonObj;
    }

    public void setCheckedSettingJsonObj(LinkedHashMap<String, String> checkedSettingJsonObj) {
        this.checkedSettingJsonObj = checkedSettingJsonObj;
    }

    public LinkedHashMap<String, Object> getSettingJsonObj() {
        return settingJsonObj;
    }

    public void setSettingJsonObj(LinkedHashMap<String, Object> settingJsonObj) {
        this.settingJsonObj = settingJsonObj;
    }

    public Integer getWidth() {
        return width;
    }

    public void setWidth(Integer width) {
        this.width = width;
    }

    public Integer getHeight() {
        return height;
    }

    public void setHeight(Integer height) {
        this.height = height;
    }

    public String getSizeStr() {
        return sizeStr;
    }

    public void setSizeStr(String sizeStr) {
        this.sizeStr = sizeStr;
    }

    public String getShowFieldLabels() {
        return showFieldLabels;
    }

    public void setShowFieldLabels(String showFieldLabels) {
        this.showFieldLabels = showFieldLabels;
    }

    public String getUpdateMemberName() {
        return updateMemberName;
    }

    public void setUpdateMemberName(String updateMemberName) {
        this.updateMemberName = updateMemberName;
    }


    /**
     * json内的字段模型对象
     */
    public static class SettingJsonObject implements Serializable {
        private String key;
        private String label;
        private Boolean enable;

        public String getKey() {
            return key;
        }

        public void setKey(String key) {
            this.key = key;
        }

        public String getLabel() {
            return label;
        }

        public void setLabel(String label) {
            this.label = label;
        }

        public Boolean getEnable() {
            return enable;
        }

        public void setEnable(Boolean enable) {
            this.enable = enable;
        }
    }

}
