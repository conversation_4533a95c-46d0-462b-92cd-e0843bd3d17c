package com.zxy.product.system.content;

import java.util.Objects;

public enum SizeEnum {
    COURSE("课程封面", 0),
    SUBJECT("专题封面", 1),
    BIG_BANNER("虚拟空间大banner", 2),
    SMALL_BANNER("虚拟空间小banner", 3),
    SUBJECT_BANNER("专题banner", 4);


    private String name;
    private Integer type;

    /**
     *
     * @param name
     * @param value
     */
    SizeEnum(String name, Integer value) {
        this.name = name;
        this.type = value;
    }

    public static String getNameByKey(Integer key) {
        for (SizeEnum e : SizeEnum.values()) {
            if (Objects.equals(e.getType(),key)) {
                return e.getName();
            }
        }
        return null; // 如果没有找到key，返回null
    }


    public String getName() {
        return name;
    }

    public int getType() {
        return type;
    }
}
