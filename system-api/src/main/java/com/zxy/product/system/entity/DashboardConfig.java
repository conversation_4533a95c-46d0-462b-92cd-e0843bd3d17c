package com.zxy.product.system.entity;

import com.zxy.product.system.jooq.tables.pojos.DashboardConfigEntity;
import java.util.List;

public class DashboardConfig extends DashboardConfigEntity {

    private static final long serialVersionUID = 4113864608487418944L;

    public static final String URI = "system/management";
    public static final Integer STATUS_ENABLE = 1;
    public static final String CACHE_KEY = "find-org";
    List<AudienceItem> audienceItems;

    private Organization organization;

    public Organization getOrganization() {
        return organization;
    }

    public void setOrganization(Organization organization) {
        this.organization = organization;
    }

    public List<AudienceItem> getAudienceItems() {
        return audienceItems;
    }

    public void setAudienceItems(List<AudienceItem> audienceItems) {
        this.audienceItems = audienceItems;
    }
}
