package com.zxy.product.system.result;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date ：Created in 2021/6/28
 * @description ：video
 */
public class Video implements Serializable {
    boolean open = false;// 视频是否打开
    Barrage barrage;// 弹幕方式
    HorseRaceLamp horseRaceLamp;// 跑马灯方式
    ImageWatermark imageWatermark;// 图片水印
    TextWatermark textWatermark;// 文字水印

    public boolean isOpen() {
        return open;
    }

    public void setOpen(boolean open) {
        this.open = open;
    }

    public Barrage getBarrage() {
        return barrage;
    }

    public void setBarrage(Barrage barrage) {
        this.barrage = barrage;
    }

    public HorseRaceLamp getHorseRaceLamp() {
        return horseRaceLamp;
    }

    public void setHorseRaceLamp(HorseRaceLamp horseRaceLamp) {
        this.horseRaceLamp = horseRaceLamp;
    }

    public ImageWatermark getImageWatermark() {
        return imageWatermark;
    }

    public void setImageWatermark(ImageWatermark imageWatermark) {
        this.imageWatermark = imageWatermark;
    }

    public TextWatermark getTextWatermark() {
        return textWatermark;
    }

    public void setTextWatermark(TextWatermark textWatermark) {
        this.textWatermark = textWatermark;
    }
}
