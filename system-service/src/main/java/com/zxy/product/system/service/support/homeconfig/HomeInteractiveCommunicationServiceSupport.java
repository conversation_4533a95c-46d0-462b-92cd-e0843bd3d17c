package com.zxy.product.system.service.support.homeconfig;

import com.zxy.common.base.helper.PagedResult;
import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.askbar.api.QuestionService;
import com.zxy.product.askbar.entity.Question;
import com.zxy.product.system.api.homeconfig.HomeInteractiveCommunicationService;
import com.zxy.product.system.entity.HomeInteractiveCommunication;
import com.zxy.product.system.entity.HomeCfgCacheItem;
import com.zxy.product.system.entity.HomeModuleConfig;
import com.zxy.product.system.util.HomeCfgUtil;

import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;


import static com.zxy.product.system.jooq.Tables.HOME_INTERACTIVE_COMMUNICATION;

/**
 * 首页互动交流配置Service实现
 * 支持数据库直接操作和缓存操作两种模式
 * 缓存模式用于首页配置的临时编辑，最终通过saveAsFinal持久化到数据库
 *
 * <AUTHOR>
 * @date 2025年07月14日 14:50
 */
@Service
public class HomeInteractiveCommunicationServiceSupport implements HomeInteractiveCommunicationService {

    /** 每个模块配置下最大展示数量限制 */
    private static final Integer MAX_DISPLAY_COUNT = 3;

    // ==================== 依赖注入 ====================

    private CommonDao<HomeInteractiveCommunication> homeInteractiveCommunicationDao;
    private QuestionService questionService;
    private MongoTemplate mongoTemplate;

    @Autowired
    public void setHomeInteractiveCommunicationDao(CommonDao<HomeInteractiveCommunication> homeInteractiveCommunicationDao) {
        this.homeInteractiveCommunicationDao = homeInteractiveCommunicationDao;
    }

    @Autowired
    public void setQuestionService(QuestionService questionService) {
        this.questionService = questionService;
    }

    @Autowired
    public void setMongoTemplate(MongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }

    /**
     * 检查互动交流数据的显示状态（被缓存方法调用）
     */
    private List<Map<String, Object>> checkDisplayStatus(String ids) {
        List<Map<String, Object>> resultList = new ArrayList<>();

        if (ids == null || ids.trim().isEmpty()) {
            return resultList;
        }

        try {
            // 解析ID字符串
            List<String> idList = Arrays.asList(ids.split(","));

            // 查询互动交流配置数据
            List<HomeInteractiveCommunication> communications = homeInteractiveCommunicationDao.execute(e -> {
                return e.select(HOME_INTERACTIVE_COMMUNICATION.fields())
                    .from(HOME_INTERACTIVE_COMMUNICATION)
                    .where(HOME_INTERACTIVE_COMMUNICATION.ID.in(idList))
                    .fetch()
                    .into(HomeInteractiveCommunication.class);
            });

            // 创建通信数据的映射，方便查找
            Map<String, HomeInteractiveCommunication> commMap = communications.stream()
                .collect(Collectors.toMap(HomeInteractiveCommunication::getId, comm -> comm));

            // 获取所有需要展示的数据的问吧ID
            List<String> questionIds = communications.stream()
                .filter(comm -> comm.getIsDisplay() != null && comm.getIsDisplay() == 1)
                .map(HomeInteractiveCommunication::getQuestionId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

            // 查询问吧数据状态
            Map<String, Question> questionMap = new HashMap<>();
            if (!questionIds.isEmpty()) {
                List<Question> questions = questionService.getByIds(questionIds);
                questionMap = questions.stream()
                    .filter(Objects::nonNull)
                    .collect(Collectors.toMap(Question::getId, q -> q));
            }

            // 为每个ID生成结果
            for (String id : idList) {
                Map<String, Object> itemResult = new HashMap<>();
                itemResult.put("id", id);

                HomeInteractiveCommunication comm = commMap.get(id);
                if (comm == null) {
                    // 未找到对应的互动交流配置数据
                    itemResult.put("canDisplay", false);
                } else if (comm.getIsDisplay() == null || comm.getIsDisplay() != 1) {
                    // 互动交流数据本身不需要展示
                    itemResult.put("canDisplay", false);
                } else if (comm.getQuestionId() == null) {
                    // 没有关联的问吧数据
                    itemResult.put("canDisplay", false);
                } else {
                    // 检查问吧数据状态
                    Question question = questionMap.get(comm.getQuestionId());
                    if (question == null) {
                        // 问吧数据不存在，删除互动交流数据
                        homeInteractiveCommunicationDao.delete(id);
                        itemResult.put("canDisplay", false);
                    } else {
                        // 检查问吧数据是否被隐藏、删除或红船审核未通过
                        boolean isInvalid = (question.getDeleteFlag() != null && Question.DELETE_FLAG_YES.equals(question.getDeleteFlag())) ||
                                           (question.getHidden() != null && Question.HIDDEN_FLAG_YES.equals(question.getHidden())) ||
                                           !Question.RED_BOAT_AUDIT_STATUS_UNDER_REVIEW.equals(question.getRedBoatQuestionAuditStatus());

                        if (isInvalid) {
                            // 问吧数据被隐藏、删除或红船审核未通过，删除互动交流数据
                            homeInteractiveCommunicationDao.delete(id);
                            itemResult.put("canDisplay", false);
                        } else {
                            // 问吧数据正常，能展示
                            itemResult.put("canDisplay", true);
                        }
                    }
                }

                resultList.add(itemResult);
            }

        } catch (Exception e) {
            // 发生异常时，为每个ID返回不能展示的状态
            List<String> idList = Arrays.asList(ids.split(","));
            resultList.clear();
            for (String id : idList) {
                Map<String, Object> itemResult = new HashMap<>();
                itemResult.put("id", id);
                itemResult.put("canDisplay", false);
                resultList.add(itemResult);
            }
        }

        return resultList;
    }

    @Override
    public PagedResult<HomeInteractiveCommunication> findPageFromCache(String userId, String userToken, Integer page, Integer pageSize, String moduleConfigId) {
        // 获取模块配置信息
        HomeModuleConfig cfg = HomeCfgUtil.getDataFromCache(userId, userToken, HomeCfgCacheItem.M_MODULE
                , null, null, moduleConfigId, mongoTemplate);

        // 从缓存获取所有数据
        List<HomeInteractiveCommunication> allItems = loadItemsFromCache(userId, userToken, cfg, moduleConfigId);

        if (allItems == null || allItems.isEmpty()) {
            return PagedResult.create(0, new ArrayList<>());
        }

        // 关联问吧数据并验证有效性，同时更新缓存
        allItems = associateAndValidateQuestionDataForCache(userId, userToken, cfg, moduleConfigId, allItems);

        // 对数据进行排序
        sortItems(allItems);

        // 创建分页结果
        return createPagedResult(allItems, page, pageSize);
    }

    @Override
    public HomeInteractiveCommunication getFromCache(String userId, String userToken, String id) {
        return HomeCfgUtil.getDataFromCache(userId, userToken, HomeCfgCacheItem.M_INTERACTIVE_COMMUNICATION
                , null, null, id, mongoTemplate);
    }

    @Override
    public HomeInteractiveCommunication insertToCache(String userId, String userToken, String moduleConfigId, String questionId, Integer isDisplay) {
        // 获取模块配置信息
        HomeModuleConfig cfg = HomeCfgUtil.getDataFromCache(userId, userToken, HomeCfgCacheItem.M_MODULE
                , null, null, moduleConfigId, mongoTemplate);

        // 检查该模块配置下是否已存在相同的questionId，防止重复添加
        boolean exists = HomeCfgUtil.existsByFieldID(userId, userToken, HomeCfgCacheItem.M_INTERACTIVE_COMMUNICATION
                , cfg.getHomeConfigId(), moduleConfigId, "questionId", questionId, mongoTemplate);

        if (exists) {
            // 如果已存在，直接返回null或抛出异常，这里选择返回null
            return null;
        }

        HomeInteractiveCommunication entity = new HomeInteractiveCommunication();
        entity.forInsert();
        entity.setModuleConfigId(moduleConfigId);
        entity.setQuestionId(questionId);
        entity.setIsDisplay(isDisplay);

        HomeCfgUtil.addItem(userId, userToken, HomeCfgCacheItem.M_INTERACTIVE_COMMUNICATION
                , cfg.getHomeConfigId(), moduleConfigId, entity, null, mongoTemplate);
        return entity;
    }

    @Override
    public List<HomeInteractiveCommunication> batchInsertToCache(String userId, String userToken, String moduleConfigId, List<String> questionIds, Integer isDisplay) {
        // 获取模块配置信息
        HomeModuleConfig cfg = HomeCfgUtil.getDataFromCache(userId, userToken, HomeCfgCacheItem.M_MODULE
                , null, null, moduleConfigId, mongoTemplate);

        List<HomeInteractiveCommunication> results = new ArrayList<>();
        for (String questionId : questionIds) {
            // 检查该模块配置下是否已存在相同的questionId，防止重复添加
            boolean exists = HomeCfgUtil.existsByFieldID(userId, userToken, HomeCfgCacheItem.M_INTERACTIVE_COMMUNICATION
                    , cfg.getHomeConfigId(), moduleConfigId, "questionId", questionId, mongoTemplate);

            if (exists) {
                // 如果已存在，跳过该questionId
                continue;
            }

            HomeInteractiveCommunication entity = new HomeInteractiveCommunication();
            entity.forInsert();
            entity.setModuleConfigId(moduleConfigId);
            entity.setQuestionId(questionId);
            entity.setIsDisplay(isDisplay);

            HomeCfgUtil.addItem(userId, userToken, HomeCfgCacheItem.M_INTERACTIVE_COMMUNICATION
                    , cfg.getHomeConfigId(), moduleConfigId, entity, null, mongoTemplate);
            results.add(entity);
        }
        return results;
    }

    @Override
    public Map<String, Object> updateToCache(String userId, String userToken, String id, String moduleConfigId, Integer isDisplay) {
        // 获取模块配置信息
        HomeModuleConfig cfg = HomeCfgUtil.getDataFromCache(userId, userToken, HomeCfgCacheItem.M_MODULE
                , null, null, moduleConfigId, mongoTemplate);

        HomeInteractiveCommunication entity = HomeCfgUtil.getDataFromCache(userId, userToken
                , HomeCfgCacheItem.M_INTERACTIVE_COMMUNICATION, cfg.getHomeConfigId(), moduleConfigId, id, mongoTemplate);

        if (entity == null) {
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "记录不存在");
            return result;
        }

        // 检查展示数量限制（这里应该从缓存中检查，而不是从数据库）
        if (isDisplay != null && isDisplay == 1) {
            // 从缓存中统计当前展示的数量
            Integer currentDisplayCount = countDisplayedFromCache(userId, userToken, cfg.getHomeConfigId(), moduleConfigId, id);
            if (currentDisplayCount >= MAX_DISPLAY_COUNT) {
                Map<String, Object> result = new HashMap<>();
                result.put("success", false);
                result.put("message", "该模块配置下展示的数据已达到上限（" + MAX_DISPLAY_COUNT + "条）");
                return result;
            }
        }

        entity.setModuleConfigId(moduleConfigId);
        entity.setIsDisplay(isDisplay);

        HomeCfgUtil.updateItem(userId, userToken, HomeCfgCacheItem.M_INTERACTIVE_COMMUNICATION
                , cfg.getHomeConfigId(), moduleConfigId, id, entity, null, mongoTemplate);

        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("id", id);
        return result;
    }

    @Override
    public String deleteFromCache(String userId, String userToken, String id) {
        HomeCfgUtil.deleteItemWithFlag(userId, userToken, HomeCfgCacheItem.M_INTERACTIVE_COMMUNICATION
                , null, null, id, mongoTemplate);
        return id;
    }

    @Override
    public List<Map<String, Object>> checkDisplayStatusFromCache(String userId, String userToken, String ids) {
        // 对于缓存版本，直接调用原方法，因为这个方法主要是检查数据状态
        return checkDisplayStatus(ids);
    }

    @Override
    public void saveAsFinal(String userId, String userToken, String homeConfigId) {
        HomeCfgUtil.saveListAsFinal(userId, userToken, HomeCfgCacheItem.M_INTERACTIVE_COMMUNICATION
                , homeConfigId, null, mongoTemplate
                , (HomeCfgCacheItem<HomeInteractiveCommunication> item) -> {
                    homeInteractiveCommunicationDao.delete(item.getData().getId());
                }
                , (HomeCfgCacheItem<HomeInteractiveCommunication> item) -> {
                    homeInteractiveCommunicationDao.insert(item.getData());
                }
                , (HomeCfgCacheItem<HomeInteractiveCommunication> item) -> {
                    homeInteractiveCommunicationDao.update(item.getData());
                }
                , (HomeCfgCacheItem<HomeInteractiveCommunication> item) -> {
                    homeInteractiveCommunicationDao.update(item.getData());
                });
    }

    /**
     * 从缓存加载数据
     */
    private List<HomeInteractiveCommunication> loadItemsFromCache(String userId, String userToken, HomeModuleConfig cfg, String moduleConfigId) {
        return HomeCfgUtil.findAndLoadListFromCache(userId, userToken
                , cfg.getHomeConfigId(), HomeCfgCacheItem.M_INTERACTIVE_COMMUNICATION, moduleConfigId
                , mongoTemplate, () -> queryAllByModuleConfigId(moduleConfigId)
                , HomeCfgUtil.getQuery(userId, userToken, HomeCfgCacheItem.M_INTERACTIVE_COMMUNICATION, cfg.getHomeConfigId(), moduleConfigId, true)
                , (HomeInteractiveCommunication data) -> data.getId());
    }

    /**
     * 关联问吧数据并验证有效性（缓存版本）
     */
    private List<HomeInteractiveCommunication> associateAndValidateQuestionDataForCache(
            String userId, String userToken, HomeModuleConfig cfg, String moduleConfigId, List<HomeInteractiveCommunication> items) {

        // 分离已填充和未填充的数据
        List<HomeInteractiveCommunication> filledItems = new ArrayList<>();
        List<HomeInteractiveCommunication> unfilledItems = new ArrayList<>();

        for (HomeInteractiveCommunication item : items) {
            if (isQuestionDataFilled(item)) {
                filledItems.add(item);
            } else {
                unfilledItems.add(item);
            }
        }

        // 处理未填充的数据
        List<HomeInteractiveCommunication> processedUnfilledItems = new ArrayList<>();
        if (!unfilledItems.isEmpty()) {
            // 提取questionId列表
            List<String> questionIds = unfilledItems.stream()
                .map(HomeInteractiveCommunication::getQuestionId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

            if (!questionIds.isEmpty()) {
                // 批量查询问吧信息
                List<Question> questions = questionService.getByIds(questionIds);

                // 创建有效问吧ID集合
                Set<String> validQuestionIds = getValidQuestionIds(questions);

                // 删除无效记录（从缓存中删除）
                removeInvalidRecordsFromCache(userId, userToken, cfg, moduleConfigId, unfilledItems, validQuestionIds);

                // 过滤有效记录并填充问吧数据，同时更新缓存
                processedUnfilledItems = fillQuestionDataForValidItemsAndUpdateCache(userId, userToken, cfg, moduleConfigId, unfilledItems, questions, validQuestionIds);
            }
        }

        // 对已填充的数据也需要验证有效性（但不需要重新填充）
        List<HomeInteractiveCommunication> validFilledItems = validateFilledItems(userId, userToken, cfg, moduleConfigId, filledItems);

        // 合并结果
        List<HomeInteractiveCommunication> result = new ArrayList<>();
        result.addAll(validFilledItems);
        result.addAll(processedUnfilledItems);

        return result;
    }

    /**
     * 判断问吧数据是否已经填充
     */
    private boolean isQuestionDataFilled(HomeInteractiveCommunication item) {
        // 通过检查type字段是否不为空来判断是否已填充问吧数据
        return item.getType() != null;
    }

    /**
     * 验证已填充数据的有效性（不重新填充，只验证问吧数据是否仍然有效）
     */
    private List<HomeInteractiveCommunication> validateFilledItems(
            String userId, String userToken, HomeModuleConfig cfg, String moduleConfigId, List<HomeInteractiveCommunication> filledItems) {

        if (filledItems.isEmpty()) {
            return new ArrayList<>();
        }

        // 提取questionId列表进行验证
        List<String> questionIds = filledItems.stream()
            .map(HomeInteractiveCommunication::getQuestionId)
            .filter(Objects::nonNull)
            .collect(Collectors.toList());

        if (questionIds.isEmpty()) {
            return new ArrayList<>();
        }

        // 批量查询问吧信息进行验证
        List<Question> questions = questionService.getByIds(questionIds);
        Set<String> validQuestionIds = getValidQuestionIds(questions);

        // 删除无效记录（从缓存中删除）
        removeInvalidRecordsFromCache(userId, userToken, cfg, moduleConfigId, filledItems, validQuestionIds);

        // 只返回有效的记录，不重新填充数据
        return filledItems.stream()
            .filter(item -> validQuestionIds.contains(item.getQuestionId()))
            .collect(Collectors.toList());
    }

    /**
     * 从缓存中删除无效记录
     */
    private void removeInvalidRecordsFromCache(String userId, String userToken, HomeModuleConfig cfg, String moduleConfigId,
            List<HomeInteractiveCommunication> items, Set<String> validQuestionIds) {
        List<String> invalidRecordIds = items.stream()
            .filter(item -> !validQuestionIds.contains(item.getQuestionId()))
            .map(HomeInteractiveCommunication::getId)
            .collect(Collectors.toList());

        if (!invalidRecordIds.isEmpty()) {
            for (String invalidId : invalidRecordIds) {
                HomeCfgUtil.deleteItemWithFlag(userId, userToken, HomeCfgCacheItem.M_INTERACTIVE_COMMUNICATION
                        , cfg.getHomeConfigId(), moduleConfigId, invalidId, mongoTemplate);
            }
        }
    }

    /**
     * 为有效记录填充问吧数据并更新缓存
     */
    private List<HomeInteractiveCommunication> fillQuestionDataForValidItemsAndUpdateCache(
            String userId, String userToken, HomeModuleConfig cfg, String moduleConfigId,
            List<HomeInteractiveCommunication> items, List<Question> questions, Set<String> validQuestionIds) {

        // 创建问吧数据映射
        Map<String, Question> questionMap = questions.stream()
            .filter(Objects::nonNull)
            .filter(q -> q.getId() != null)
            .collect(Collectors.toMap(Question::getId, q -> q, (existing, replacement) -> existing));

        // 过滤有效记录并填充问吧数据
        List<HomeInteractiveCommunication> validItems = items.stream()
            .filter(item -> validQuestionIds.contains(item.getQuestionId()))
            .collect(Collectors.toList());

        // 为有效记录填充问吧数据并更新缓存
        validItems.forEach(item -> {
            Question question = questionMap.get(item.getQuestionId());
            if (question != null) {
                fillQuestionData(item, question);
                // 更新缓存
                HomeCfgUtil.updateItem(userId, userToken, HomeCfgCacheItem.M_INTERACTIVE_COMMUNICATION
                        , cfg.getHomeConfigId(), moduleConfigId, item.getId(), item, null, mongoTemplate);
            }
        });

        return validItems;
    }

    /**
     * 对数据进行排序
     */
    private void sortItems(List<HomeInteractiveCommunication> items) {
        items.sort((item1, item2) -> {
            // 首先按 isDisplay 降序排序（1在前，0在后）
            Integer isDisplay1 = item1.getIsDisplay() != null ? item1.getIsDisplay() : 0;
            Integer isDisplay2 = item2.getIsDisplay() != null ? item2.getIsDisplay() : 0;
            int displayCompare = isDisplay2.compareTo(isDisplay1);

            if (displayCompare != 0) {
                return displayCompare;
            }

            // 如果 isDisplay 相同，则按 createTime 降序排序（新的在前）
            if (item1.getCreateTime() != null && item2.getCreateTime() != null) {
                return item2.getCreateTime().compareTo(item1.getCreateTime());
            } else if (item1.getCreateTime() != null) {
                return -1;
            } else if (item2.getCreateTime() != null) {
                return 1;
            } else {
                return 0;
            }
        });
    }

    /**
     * 查询指定模块配置下的所有互动交流配置
     * 注意：不在数据库层面排序，因为后续会在内存中删除无效记录、填充问吧数据并重新排序
     */
    private List<HomeInteractiveCommunication> queryAllByModuleConfigId(String moduleConfigId) {
        List<HomeInteractiveCommunication> items = homeInteractiveCommunicationDao.execute(e ->
            e.select(Fields.start().add(HOME_INTERACTIVE_COMMUNICATION.fields()).end())
                .from(HOME_INTERACTIVE_COMMUNICATION)
                .where(HOME_INTERACTIVE_COMMUNICATION.MODULE_CONFIG_ID.eq(moduleConfigId))
                .fetch()
                .into(HomeInteractiveCommunication.class));
        return items != null ? items : new ArrayList<>();
    }

    /**
     * 获取有效的问吧ID集合
     */
    private Set<String> getValidQuestionIds(List<Question> questions) {
        return questions.stream()
            .filter(Objects::nonNull)
            .filter(q -> q.getId() != null)
            .filter(q -> Question.DELETE_FLAG_NO.equals(q.getDeleteFlag()))
            .filter(q -> Question.HIDDEN_FLAG_NO.equals(q.getHidden()))
            .filter(q -> Question.RED_BOAT_AUDIT_STATUS_UNDER_REVIEW.equals(q.getRedBoatQuestionAuditStatus()))
            .map(Question::getId)
            .collect(Collectors.toSet());
    }

    /**
     * 填充单个记录的问吧数据
     */
    private void fillQuestionData(HomeInteractiveCommunication item, Question question) {
        if (question != null) {
            item.setTitle(question.getTitle());
            item.setType(question.getType());
            item.setCreateMemberId(question.getCreateMemberId());
            item.setQuestionCreateTime(question.getCreateTime());
            if (question.getCreateMember() != null) {
                item.setCreateMemberFullName(question.getCreateMember().getFullName());
            }
        }
    }

    /**
     * 创建分页结果
     */
    private PagedResult<HomeInteractiveCommunication> createPagedResult(
            List<HomeInteractiveCommunication> items, Integer page, Integer pageSize) {
        int totalCount = items.size();
        int startIndex = (page - 1) * pageSize;
        int endIndex = Math.min(startIndex + pageSize, totalCount);

        List<HomeInteractiveCommunication> pageItems = new ArrayList<>();
        if (startIndex < totalCount) {
            pageItems = items.subList(startIndex, endIndex);
        }

        return PagedResult.create(totalCount, pageItems);
    }

    /**
     * 从缓存中统计指定模块配置下展示的数据数量
     */
    private Integer countDisplayedFromCache(String userId, String userToken, String homeConfigId, String moduleConfigId, String excludeId) {
        org.springframework.data.mongodb.core.query.Query query = HomeCfgUtil.getQuery(userId, userToken,
                HomeCfgCacheItem.M_INTERACTIVE_COMMUNICATION, homeConfigId, moduleConfigId, true);

        query.addCriteria(org.springframework.data.mongodb.core.query.Criteria.where("data.isDisplay").is(1));

        if (excludeId != null) {
            query.addCriteria(org.springframework.data.mongodb.core.query.Criteria.where("data._id").ne(excludeId));
        }

        return (int) mongoTemplate.count(query, HomeCfgCacheItem.class);
    }
}
