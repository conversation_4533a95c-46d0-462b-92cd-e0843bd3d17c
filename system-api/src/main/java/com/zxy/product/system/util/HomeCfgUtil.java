package com.zxy.product.system.util;

import com.zxy.common.base.helper.PagedResult;
import com.zxy.product.system.entity.HomeCfgCacheItem;
import com.zxy.product.system.entity.HomeModuleConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.domain.Sort.Order;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;

import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.locks.ReentrantLock;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * 首页配置相关操作的公共方法
 *
 */
public abstract class HomeCfgUtil {

	private final static Logger LOGGER = LoggerFactory.getLogger(HomeCfgUtil.class);

	private static SegmentLock<String> segmentLock = new SegmentLock<String>(32, false);


	/**
	 * 缺省数据过期时间间隔，单位：小时
	 */
	public static final int DEFAULT_EXPIRED_HOURS = 12;

    /**
     * 根据指定条件返回一个查询对象
     * @param userId
     * @param userToken
     * @param module
     * @param homeCfgId
     * @param moduleId
     * @param filterDel 是否过滤掉有删除标志的数据
     * @return
     */
    public static Query getQuery(String userId, String userToken, String module, String homeCfgId, String moduleId
    		, boolean filterDel) {
		Query query = new Query();
		Criteria criteria = Criteria.where("userId").is(userId);
		if (userToken != null) {
			criteria = criteria.and("userToken").is(userToken);
		}
		criteria = criteria.and("module").is(module);
		if (homeCfgId != null) {
			criteria = criteria.and("homeCfgId").is(homeCfgId);
		}
		if (moduleId != null) {
			criteria = criteria.and("moduleId").is(moduleId);
		}
		if (filterDel) {
			criteria = criteria.and(HomeCfgCacheItem.F_OPT_DEL).ne(true);
		}
		query.addCriteria(criteria);
		return query;
    }


    /**
     * 更新指定数据的序号，并对整个符合条件的列表进行重排序
     * @param userId
     * @param userToken
     * @param module
     * @param homeCfgId
     * @param moduleId
     * @param id 当前更新排序的数据ID
     * @param sortField 排序字段名
     * @param oldSort 旧序号
     * @param newSort 新序号
     * @param mongoTemplate
     * @return
     */
    public static String sort(String userId, String userToken, String module
    		, String homeCfgId, String moduleId, String id, String sortField, int oldSort
    		, int newSort, MongoTemplate mongoTemplate) {
    	Query query = HomeCfgUtil.getQuery(userId, userToken, module, homeCfgId, moduleId, true);
    	Update update = new Update();
    	if (newSort > oldSort) {
    		query.addCriteria(Criteria.where("data." + sortField)
    				.gt(oldSort).lte(newSort).ne(id));
    		update.inc("data." + sortField, -1);
    		update.set(HomeCfgCacheItem.F_OPT_SORT, true);
        	mongoTemplate.updateMulti(query, update, HomeCfgCacheItem.class);
    	}else if (newSort < oldSort) {
    		query.addCriteria(Criteria.where("data." + sortField)
    				.gte(newSort).lt(oldSort).ne(id));
    		update.inc("data." + sortField, 1);
    		update.set(HomeCfgCacheItem.F_OPT_SORT, true);
        	mongoTemplate.updateMulti(query, update, HomeCfgCacheItem.class);
    	}

    	query = HomeCfgUtil.getQuery(userId, userToken, module, homeCfgId, moduleId, true);
    	query.addCriteria(Criteria.where("data._id").is(id));
    	update = new Update();
    	update.set("data." + sortField, newSort);
		update.set(HomeCfgCacheItem.F_OPT_SORT, true);
    	mongoTemplate.updateFirst(query, update, HomeCfgCacheItem.class);
    	return id;

    }

    /**
     * 获取单条记录数据（用于不需分页，只包含单条数据的业务)
     * @param userId
     * @param userToken
     * @param module
     * @param homeCfgId
     * @param moduleId
     * @param paras
     * @param mongoTemplate
     * @param daoQuery
     * @return
     */
    public static <T> T findAndLoadOneFromCache(String userId, String userToken
    		, String module, String homeCfgId, String moduleId, Map<String, Object> paras, MongoTemplate mongoTemplate
    		, Supplier<T> daoQuery) {
    	String key = getLockKey(userId, userToken, module, homeCfgId);
    	try {
    		lock(key);
    		Query query = HomeCfgUtil.getQuery(userId, userToken, module, homeCfgId, moduleId, false);
    		if (paras != null && paras.size() > 0) {
    			Criteria c = new Criteria();
    			for (Entry<String, Object> e : paras.entrySet()) {
    				c = c.and(e.getKey()).is(e.getValue());
    			}
    			query.addCriteria(c);
    		}

    		T result = null;
    		long count = mongoTemplate.count(query, HomeCfgCacheItem.COLL_NAME);
    		if (count == 0) {
    			T data = daoQuery.get();
    			if (data != null) {
    				upsertItem(data, null, false, mongoTemplate, query);
    			}
    			result = data;
    		}else if (count > 0) {
    			query = HomeCfgUtil.getQuery(userId, userToken, module, homeCfgId, moduleId, true);
    			if (paras != null && paras.size() > 0) {
    				Criteria c = new Criteria();
    				for (Entry<String, Object> e : paras.entrySet()) {
    					c = c.and(e.getKey()).is(e.getValue());
    				}
    				query.addCriteria(c);
    			}
    			@SuppressWarnings("unchecked")
    			HomeCfgCacheItem<T> item = mongoTemplate.findOne(query, HomeCfgCacheItem.class);
    			if (item != null) {
    				result = item.getData();
    			}
    		}
    		return result;
    	}finally {
    		unlock(key);
    	}
    }

    /**
     * 获取数据记录包含的原始对象
     * @param userId
     * @param userToken
     * @param module
     * @param homeCfgId
     * @param moduleId
     * @param dataId 对象数据ID
     * @param mongoTemplate
     * @return
     */
    @SuppressWarnings("unchecked")
	public static <T> T getDataFromCache(String userId, String userToken
    		, String module, String homeCfgId, String moduleId, String dataId, MongoTemplate mongoTemplate) {
    	HomeCfgCacheItem<T> item = getItem(userId, userToken, module, homeCfgId, moduleId, dataId, mongoTemplate);
		return !Objects.isNull(item)?item.getData():null;
		/*return (T) getItem(userId, userToken, module, homeCfgId, moduleId, dataId, mongoTemplate).getData();*/
    }


    /**
     * 根据指定的ID字段，判断数据是否已存在
     * @param userId
     * @param userToken
     * @param module
     * @param homeCfgId
     * @param moduleId
     * @param idField ID字段名
     * @param id ID值
     * @param mongoTemplate
     * @return
     */
    public static boolean existsByFieldID(String userId, String userToken
    		, String module, String homeCfgId, String moduleId
    		, String idField, String id, MongoTemplate mongoTemplate) {
    	Query query = getQuery(userId, userToken, module, homeCfgId, moduleId, true);
    	query.addCriteria(Criteria.where("data." + idField).is(id));
    	return mongoTemplate.exists(query, HomeCfgCacheItem.class);
    }


    /**
     * 根据条件获取单条数据记录
     * @param userId
     * @param userToken
     * @param module
     * @param homeCfgId
     * @param moduleId
     * @param dataId
     * @param mongoTemplate
     * @return
     */
    public static <T> HomeCfgCacheItem<T> getItem(String userId, String userToken
    		, String module, String homeCfgId, String moduleId, String dataId, MongoTemplate mongoTemplate) {
    	Query query = getQuery(userId, userToken, module, homeCfgId, moduleId, false);
    	if (dataId != null) {
        	query.addCriteria(Criteria.where("data._id").is(dataId));
    	}
    	@SuppressWarnings("unchecked")
		HomeCfgCacheItem<T> item = mongoTemplate.findOne(query, HomeCfgCacheItem.class);
    	return item;
    }


    /**
     * 获取有过更新操作的数据记录
     * @param userId
     * @param userToken
     * @param module
     * @param moduleId
     * @param dataId
     * @param mongoTemplate
     * @return
     */
    public static <T> HomeCfgCacheItem<T> getItemWithUpdate(String userId, String userToken
    		, String module, String homeCfgId, String moduleId, String dataId, MongoTemplate mongoTemplate) {
    	Query query = getQuery(userId, userToken, module, homeCfgId, moduleId, false);
    	if (dataId != null) {
        	query.addCriteria(Criteria.where("data._id").is(dataId));
    	}
    	setUpdateOptCriteria(query);
    	@SuppressWarnings("unchecked")
		HomeCfgCacheItem<T> item = mongoTemplate.findOne(query, HomeCfgCacheItem.class);
    	return item;
    }


    /**
     * 查询所有更新过的数据列表（包括增，删，改，排序等操作）
     * @param userId
     * @param userToken
     * @param module
     * @param homeCfgId
     * @param moduleId
     * @param mongoTemplate
     * @return
     */
    @SuppressWarnings("unchecked")
	public static <T> List<HomeCfgCacheItem<T>> getListItemWithUpdate(String userId, String userToken
    		, String module, String homeCfgId, String moduleId, MongoTemplate mongoTemplate) {
    	Query query = getQuery(userId, userToken, module, homeCfgId, moduleId, false);
    	setUpdateOptCriteria(query);
    	List<? super HomeCfgCacheItem<T>> item = mongoTemplate.find(query, HomeCfgCacheItem.class);
    	return (List<HomeCfgCacheItem<T>>) item;
    }


    /**
     * 配置更新操作相关的条件（增，删，改等)
     * @param query
     */
    private static void setUpdateOptCriteria(Query query) {
    	Criteria criteria = new Criteria();
    	query.addCriteria(criteria.orOperator(Criteria.where(HomeCfgCacheItem.F_OPT_ADD).is(true),
    			Criteria.where(HomeCfgCacheItem.F_OPT_EDIT).is(true),
    			Criteria.where(HomeCfgCacheItem.F_OPT_EDIT_EXT).is(true),
    			Criteria.where(HomeCfgCacheItem.F_OPT_SORT).is(true),
    			Criteria.where(HomeCfgCacheItem.F_OPT_DEL).is(true)));
    }


    /**
     * 根据条件判断数据是否已存在
     * @param userId
     * @param userToken
     * @param module
     * @param homeCfgId
     * @param moduleId
     * @param dataId
     * @param mongoTemplate
     * @return true: 存在, false: 不存在
     */
    public static boolean existsItem(String userId, String userToken
    		, String module, String homeCfgId, String moduleId, String dataId, MongoTemplate mongoTemplate) {
    	Query query = getQuery(userId, userToken, module, homeCfgId, moduleId, false);
    	if (dataId != null) {
        	query.addCriteria(Criteria.where("data._id").is(dataId));
    	}
    	return mongoTemplate.exists(query, HomeCfgCacheItem.class);
    }



    /**
     * 增加数据
     * @param userId
     * @param userToken
     * @param module
     * @param homeCfgId
     * @param moduleId
     * @param data
     * @param extData
     * @param mongoTemplate
     */
    public static <T> void addItem(String userId, String userToken
    		, String module, String homeCfgId, String moduleId, T data, Map<String, Object> extData, MongoTemplate mongoTemplate) {
    	insertItem(userId, userToken, module, homeCfgId, moduleId, data, extData, true, mongoTemplate);
    }


    /**
     * 删除数据，只更新删除标志
     * @param userId
     * @param userToken
     * @param module
     * @param homeCfgId
     * @param moduleId
     * @param id
     * @param mongoTemplate
     * @return
     */
    public static String deleteItemWithFlag(String userId, String userToken
    		, String module, String homeCfgId, String moduleId, String id, MongoTemplate mongoTemplate) {
    	Query query = HomeCfgUtil.getQuery(userId, userToken, module, homeCfgId, moduleId, false);
    	if (id != null) {
        	query.addCriteria(Criteria.where("data._id").is(id));
    	}
    	Update update = new Update();
    	update.set(HomeCfgCacheItem.F_OPT_DEL, true);
    	update.set(HomeCfgCacheItem.F_LAST_UPDATE_TIME, new Date());
    	mongoTemplate.updateFirst(query, update, HomeCfgCacheItem.class);
    	return id;
    }

    /**
     * 删除数据(真实删除)
     * @param userId
     * @param userToken
     * @param module
     * @param homeCfgId
     * @param moduleId
     * @param id
     * @param mongoTemplate
     */
    public static void deleteItem(String userId, String userToken
    		, String module, String homeCfgId, String moduleId, String id, MongoTemplate mongoTemplate) {
    	Query query = HomeCfgUtil.getQuery(userId, userToken, module, homeCfgId, moduleId, false);
    	if (id != null) {
        	query.addCriteria(Criteria.where("data._id").is(id));
    	}
    	mongoTemplate.remove(query, HomeCfgCacheItem.class);
    }


    /**
     * 更新数据
     * @param userId
     * @param userToken
     * @param module
     * @param homeCfgId
     * @param moduleId
     * @param dataId
     * @param data
     * @param extData 扩展数据，如：主从关联数据结构
     * @param mongoTemplate
     */
    public static <T> void updateItem(String userId, String userToken
    		, String module, String homeCfgId, String moduleId, String dataId, T data
    		, Map<String, Object> extData, MongoTemplate mongoTemplate) {
    	Query query = HomeCfgUtil.getQuery(userId, userToken, module, homeCfgId, moduleId, true);
    	if (dataId != null) {
        	query.addCriteria(Criteria.where("data._id").is(dataId));
    	}
    	updateItem(query, data, extData, mongoTemplate);
    }

	public static <T> void updateItemDataExtAndUrlAndDataNameAndSort(String userId, String userToken
			, String module, String homeCfgId, String moduleId, String dataId, String dataExt, String dataName,String url, Integer sort, Integer appSort, MongoTemplate mongoTemplate) {
		Query query = HomeCfgUtil.getQuery(userId, userToken, module, homeCfgId, moduleId, true);
		if (dataId != null) {
			query.addCriteria(Criteria.where("data._id").is(dataId));
		}
		Update update = new Update();
		if(dataExt !=null) {
			update.set("data.dataExt", dataExt);
			update.set(HomeCfgCacheItem.F_OPT_SORT, true);
		}
        if(dataName !=null) {
            update.set("data.dataName", dataName);
            update.set(HomeCfgCacheItem.F_OPT_SORT, true);
        }
		if(url !=null){
            update.set("data.url", url);
            update.set(HomeCfgCacheItem.F_OPT_SORT, true);
        }
		if(sort !=null){
			update.set("data.sort", sort);
			update.set(HomeCfgCacheItem.F_OPT_SORT, true);
		}
		if(appSort !=null){
			update.set("data.appSort", appSort);
			update.set(HomeCfgCacheItem.F_OPT_SORT, true);
		}
		mongoTemplate.updateFirst(query, update, HomeCfgCacheItem.class);
	}


    /**
     * 根据查询条件更新数据
     * @param query
     * @param data
     * @param extData 扩展数据，如：主从关联数据结构
     * @param mongoTemplate
     */
    public static <T> void updateItem(Query query, T data
    		, Map<String, Object> extData, MongoTemplate mongoTemplate) {
    	Update update = new Update();
    	if (data != null) {
        	update.set("data", data);
        	update.set(HomeCfgCacheItem.F_OPT_EDIT, true);
    	}
    	if (extData != null) {
    		update.set("extdata", extData);
        	update.set(HomeCfgCacheItem.F_OPT_EDIT_EXT, true);
    	}
    	update.set(HomeCfgCacheItem.F_LAST_UPDATE_TIME, new Date());
    	mongoTemplate.updateFirst(query, update, HomeCfgCacheItem.class);
    }



    private static <T> HomeCfgCacheItem<T> insertItem(String userId, String userToken
    		, String module, String homeCfgId, String moduleId, T data, Map<String, Object> extData, boolean optAdd, MongoTemplate mongoTemplate) {
    	HomeCfgCacheItem<T> item = new HomeCfgCacheItem<T>();
    	item.setUserId(userId);
    	item.setUserToken(userToken);
    	item.setModule(module);
    	item.setHomeCfgId(homeCfgId);
    	item.setModuleId(moduleId);
    	item.setData(data);
    	item.setExtData(extData);
    	item.setOptAdd(optAdd);
    	item.setLastUpdateTime(new Date());
    	mongoTemplate.insert(item);
    	return item;
    }

    /**
     * 根据查询条件更新或新增数据
     * @param data
     * @param extData
     * @param optAdd 如果是新增，是否添加标志
     * @param mongoTemplate
     * @param query
     */
    private static <T> void upsertItem(T data, Map<String, Object> extData
    		, boolean optAdd, MongoTemplate mongoTemplate, Query query) {
    	Update update = new Update();
    	update.set(HomeCfgCacheItem.F_DATA, data);
    	update.set(HomeCfgCacheItem.F_EXT_DATA, extData);
    	update.set(HomeCfgCacheItem.F_OPT_ADD, optAdd);
    	update.set(HomeCfgCacheItem.F_LAST_UPDATE_TIME, new Date());
    	mongoTemplate.upsert(query, update, HomeCfgCacheItem.class);
    }


    /**
     *
     * 查询符合条件的所有数据列表（不分页），如果数据为空则从数据库加载指定模块的所有数据，
     * 然后根据查询的条件参数返回相应记录
     * @param userId
     * @param userToken
     * @param homeCfgId
     * @param module
     * @param moduleId
     * @param mongoTemplate
     * @param daoFindAll
     * @param queryParas 查询条件参数
     * @param funGetDataId
     * @return
     */
    public static <T> List<T> findAndLoadListFromCache(String userId, String userToken
    		, String homeCfgId, String module, String moduleId
    		, MongoTemplate mongoTemplate, Supplier<List<T>> daoFindAll
    		, Query queryParas, Function<T, Object> funGetDataId){
    	String key = getLockKey(userId, userToken, module, homeCfgId);
    	try {
    		lock(key);
    		Query query = HomeCfgUtil.getQuery(userId, userToken, module, homeCfgId, moduleId, false);
    		long count = mongoTemplate.count(query, HomeCfgCacheItem.COLL_NAME);
    		if (count == 0) {
    			List<T> list = daoFindAll.get();
    	        list.forEach(data -> {
    	        	Query q = HomeCfgUtil.getQuery(userId, userToken, module, homeCfgId, moduleId, false);
    	        	q.addCriteria(Criteria.where("data._id").is(funGetDataId.apply(data)));
    	        	upsertItem(data, null, false, mongoTemplate, q);
    	        });
    		}
    		List<? super HomeCfgCacheItem<T>> items = mongoTemplate.find(queryParas, HomeCfgCacheItem.class);
    		List<T> list = null;
    		if (items != null) {
    			list = items.stream().map(item -> {
    				@SuppressWarnings("unchecked")
    				T data = ((HomeCfgCacheItem<T>) item).getData();
    				return data;
    			}).collect(Collectors.toList());
    		}
    		return list;
    	}finally {
    		unlock(key);
    	}

    }


    /**
     * 获取分页数据，如果数据为空，则从数据库加载一次，然后再返回
     * @param userId
     * @param userToken
     * @param page
     * @param pageSize
     * @param module
     * @param homeCfgId
     * @param moduleId
     * @param mongoTemplate
     * @param daoFindPage
     * @param funGetDataId
     * @return
     */
    public static <T> PagedResult<T> findAndLoadPageFromCache(String userId, String userToken
    		, int page, int pageSize, String module, String homeCfgId, String moduleId
    		, MongoTemplate mongoTemplate, Function<Integer, PagedResult<T>> daoFindPage
    		, Function<T, Object> funGetDataId){
    	return findAndLoadPageFromCache(userId, userToken, page, pageSize, module
    			, homeCfgId, moduleId, mongoTemplate, daoFindPage, null, funGetDataId);
    }


    public static <T> PagedResult<T> findAndLoadPageFromCache(String userId, String userToken
    		, int page, int pageSize, String module, String homeCfgId, String moduleId
    		, MongoTemplate mongoTemplate, Function<Integer, PagedResult<T>> daoFindPage
    		, Query queryParas, Function<T, Object> funGetDataId){
    	String key = getLockKey(userId, userToken, module, homeCfgId);
    	try {
    		lock(key);
    		Query query = HomeCfgUtil.getQuery(userId, userToken, module, homeCfgId, moduleId, false);
    		long count = mongoTemplate.count(query, HomeCfgCacheItem.COLL_NAME);
    		if (count == 0) {
    			int p = 0;
    			PagedResult<T> prst;
    			do {
    				prst = daoFindPage.apply(++p);
    				List<T> list = prst.getItems();
    		        list.forEach(data -> {
    		        	Query q = HomeCfgUtil.getQuery(userId, userToken, module, homeCfgId, moduleId, false);
    		        	q.addCriteria(Criteria.where("data._id").is(funGetDataId.apply(data)));
    		        	upsertItem(data, null, false, mongoTemplate, q);
    		        });
    		        count = count + prst.getRecordCount();
    			}while(prst != null && prst.getItems() != null && prst.getItems().size() > 0 && p < 100);
    		}

    		List<T> list = null;
    		if (count > 0) {
    			if (queryParas == null) {
    				query = HomeCfgUtil.getQuery(userId, userToken, module, homeCfgId, moduleId, true);
    				count = mongoTemplate.count(query, HomeCfgCacheItem.COLL_NAME);
    				query.with(new Sort(new Order(Direction.DESC,"data.state")
    						, new Order(Direction.ASC,"data.sort")
    						, new Order(Direction.ASC,"data.sort.createTime")
    						, new Order(Direction.ASC,"data.sort._createTime")));
    				query.skip((page-1) * pageSize).limit(pageSize);
					List<? super HomeCfgCacheItem<T>> items = mongoTemplate.find(query, HomeCfgCacheItem.class);

					if (items != null) {
    					list = items.stream().map(item -> {
    						@SuppressWarnings("unchecked")
    						T data = ((HomeCfgCacheItem<T>) item).getData();
    						return data;
    					}).collect(Collectors.toList());
    				}
    			}else {
    				count = mongoTemplate.count(queryParas, HomeCfgCacheItem.COLL_NAME);
    				queryParas.skip((page-1) * pageSize).limit(pageSize);
    				List<? super HomeCfgCacheItem<T>> items = mongoTemplate.find(queryParas, HomeCfgCacheItem.class);
    				if (items != null) {
    					list = items.stream().map(item -> {
    						@SuppressWarnings("unchecked")
    						T data = ((HomeCfgCacheItem<T>) item).getData();
    						return data;
    					}).collect(Collectors.toList());
    				}
    			}
    		}
    		PagedResult<T> result = (PagedResult<T>) PagedResult.create((int)count, list);
    		return result;
    	}finally {
    		unlock(key);
    	}

    }



    /**
     * 获取最大序号
     * @param userId
     * @param userToken
     * @param module
     * @param homeCfgId
     * @param moduleId
     * @param mongoTemplate
     * @param funGetSort
     * @return
     */
    public static <T> int getMaxSortInCache(String userId, String userToken, String module
    		, String homeCfgId, String moduleId, MongoTemplate mongoTemplate, Function<T, Integer> funGetSort) {
    	Query query = HomeCfgUtil.getQuery(userId, userToken, module, homeCfgId, moduleId, true);
		query.with(new Sort(new Order(Direction.DESC,"data.sort")));
		query.limit(1);
		List<? super HomeCfgCacheItem<T>> list = mongoTemplate.find(query, HomeCfgCacheItem.class);
		int maxSort = 0;
		if (list != null && list.size() > 0) {
			@SuppressWarnings("unchecked")
			HomeCfgCacheItem<T> item = (HomeCfgCacheItem<T>) list.get(0);
			T data = item.getData();
			maxSort = funGetSort.apply(data);
		}
		return maxSort;
    }

    public static <T> int getModuleMaxSortInCache(String userId, String userToken, String homeCfgId, Integer clientType,
                                                  MongoTemplate mongoTemplate) {
        Query query = HomeCfgUtil.getQuery(userId, userToken, HomeCfgCacheItem.M_MODULE, homeCfgId, null, true);
        Criteria criteria = new Criteria();
        query.addCriteria(criteria.orOperator(Criteria.where("data.clientType").is(clientType)
                , Criteria.where("data.clientType").is(0)));
        if (clientType == HomeModuleConfig.CLIENT_TYPE_PC) {
            query.with(new Sort(new Order(Direction.DESC,"data.sort")));
        }else {
            query.with(new Sort(new Order(Direction.DESC,"data.appSort")));
        }
        query.limit(1);
        HomeCfgCacheItem<T> homeCfgCacheItem = mongoTemplate.findOne(query, HomeCfgCacheItem.class);
        if (homeCfgCacheItem != null) {
            HomeModuleConfig homeModuleConfig = (HomeModuleConfig) homeCfgCacheItem.getData();
            return clientType == HomeModuleConfig.CLIENT_TYPE_PC ? homeModuleConfig.getSort() : homeModuleConfig.getAppSort();
        }
        return 0;
    }

    /**
     * 删除过期数据
     * @param hours 过期时间间隔，单位：小时
     * @param mongoTemplate
     */
    public static void deleteExpiredData(int hours, MongoTemplate mongoTemplate) {
    	long curTime = System.currentTimeMillis();
    	long exipiredTime = curTime - hours * 3600 * 1000;
    	Query query = new Query();
    	query.addCriteria(Criteria.where(HomeCfgCacheItem.F_LAST_UPDATE_TIME).lt(new Date(exipiredTime)));
    	mongoTemplate.remove(query, HomeCfgCacheItem.class);
    }


	/**
	 * 将MongoDB中当前用户修改的缓存数据保存到数据库
	 * @param userId
	 * @param userToken
	 * @param module
	 * @param homeCfgId
	 * @param moduleId
	 * @param mongoTemplate
	 * @param serviceDel
	 * @param serviceAdd
	 * @param serviceEdit
	 * @param serviceSort
	 */
	public static <T> void saveListAsFinal(String userId, String userToken, String module
			, String homeCfgId, String moduleId, MongoTemplate mongoTemplate
			, Consumer<HomeCfgCacheItem<T>> serviceDel
			, Consumer<HomeCfgCacheItem<T>> serviceAdd
			, Consumer<HomeCfgCacheItem<T>> serviceEdit
			, Consumer<HomeCfgCacheItem<T>> serviceSort) {
		List<HomeCfgCacheItem<T>> list = HomeCfgUtil.getListItemWithUpdate(userId, userToken
				, module, homeCfgId, moduleId, mongoTemplate);
		if (list != null) {
			for (HomeCfgCacheItem<T> item : list) {
				if (item.getOptDel()) {
					if (!item.getOptAdd() && serviceDel != null) {
						serviceDel.accept(item);
					}
					continue;
				}
				if (item.getOptAdd()) {
					if (serviceAdd != null) {
						serviceAdd.accept(item);
					}
			        continue;
				}
				if (item.getOptEdit()) {
					if (serviceEdit != null) {
						serviceEdit.accept(item);
					}
			        continue;
				}
				if (item.getOptSort()) {
					if (serviceSort != null) {
						serviceSort.accept(item);
					}
			        continue;
				}
			}
		}
		HomeCfgUtil.deleteItem(userId, userToken, module, homeCfgId, moduleId, null, mongoTemplate);
		HomeCfgUtil.deleteExpiredData(DEFAULT_EXPIRED_HOURS, mongoTemplate);

	}

    public static String deleteItemByModuleId(String userId, String userToken
            , String module, String homeCfgId, String moduleId, String moduleConfigId, MongoTemplate mongoTemplate) {
        Query query = HomeCfgUtil.getQuery(userId, userToken, module, homeCfgId, moduleId, false);
        mongoTemplate.remove(query, HomeCfgCacheItem.class);
        return moduleConfigId;
    }


	/**
	 * 使用并发的方式保存首页配置
	 * @param userId
	 * @param homeConfigId
	 * @param consumers
	 * @return
	 */
	public static boolean executeConcurrentSave(String userId, String homeConfigId, List<Consumer<Object>> consumers) {
		long startTime = System.currentTimeMillis();
		LOGGER.info("Start executeConcurrentSave. userId={}, homeConfigId={}", userId, homeConfigId);
		Executor executor = Executors.newFixedThreadPool(3);
		CountDownLatch latch = new CountDownLatch(consumers.size());
		// 保存每个任务的执行结果
		List<AtomicBoolean> listResults = new LinkedList<AtomicBoolean>();
		for (Consumer<Object> c : consumers) {
			AtomicBoolean rst = new AtomicBoolean();
			listResults.add(rst);
			executor.execute(() -> {
				try {
					c.accept(null);
					rst.set(true);
				}catch(Exception ex) {
					ex.getStackTrace();
					LOGGER.error("首页保存错误:{}",ex.getMessage());
					LOGGER.error("executeConcurrentSave execute task error! userId="
							+ userId + ", homeCfgId=" + homeConfigId, ex);
				}finally {
					latch.countDown();
				}
			});
		}
		boolean completed = false;
		try {
			completed = latch.await(120, TimeUnit.SECONDS);
			if (completed) {
				for (AtomicBoolean rst : listResults) {
					if (!rst.get()) {
						completed = false;
						break;
					}
				}
			}

		} catch (InterruptedException e) {
			LOGGER.error("executeConcurrentSave InterruptedException! userId={}, homeConfigId={}", userId, homeConfigId);
		}
		long times = System.currentTimeMillis() - startTime;
		LOGGER.info("End executeConcurrentSave. userId={}, homeConfigId={}, completed={}, times={}"
				, userId, homeConfigId, completed, times);
		return completed;
	}

    private static String getLockKey(String userId, String userToken, String module, String homeCfgId) {
    	return userId + "-" + userToken + "-" + module + "-" + homeCfgId;
    }


	public static void lock(String key) {
    	segmentLock.lock(key);
	}

	public static void unlock(String key) {
    	segmentLock.unlock(key);
	}

	public static class SegmentLock<T> {
	    private Integer segments = 32;//默认分段数量
	    private final ConcurrentHashMap<Integer, ReentrantLock> lockMap = new ConcurrentHashMap<>();

	    public SegmentLock() {
	        init(null, false);
	    }

	    public SegmentLock(Integer counts, boolean fair) {
	        init(counts, fair);
	    }

	    private void init(Integer counts, boolean fair) {
	        if (counts != null) {
	            segments = counts;
	        }
	        for (int i = 0; i < segments; i++) {
	            lockMap.put(i, new ReentrantLock(fair));
	        }
	    }

	    public void lock(T key) {
	        ReentrantLock lock = lockMap.get((key.hashCode()>>>1) % segments);
	        lock.lock();
	    }

	    public void unlock(T key) {
	        ReentrantLock lock = lockMap.get((key.hashCode()>>>1) % segments);
	        lock.unlock();
	    }
	}


}
