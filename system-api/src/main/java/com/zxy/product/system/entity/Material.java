package com.zxy.product.system.entity;


import com.zxy.product.system.jooq.tables.pojos.MaterialEntity;

import java.util.List;

/**
* @Description:    素材实体类
* @Author:         liuc
* @CreateDate:     2021/6/23 15:46
* @UpdateRemark:   修改内容
* @Version:        1.0
*/
public class Material extends MaterialEntity {
    private static final long serialVersionUID = 4146295752261570591L;
    public static final Integer CLOUD_FLAG_TRUE = 1;
    public static final Integer CLOUD_FLAG_FALSE = 0;

    private String categoryName;

    private String styleCode;

    private Style style;

    private PictureCategory category;
    private String size;
    private String courseCategoryName;
    private List<Material> subjectMaterialList;//关联的专题素材信息

    public String getCourseCategoryName() {
        return courseCategoryName;
    }

    public void setCourseCategoryName(String courseCategoryName) {
        this.courseCategoryName = courseCategoryName;
    }

    public String getSize() {
        return size;
    }

    public void setSize(String size) {
        this.size = size;
    }

    /**
     * 是否是云端素材的标识，1是，0否
     */
    private Integer cloudFlag;

    public Style getStyle() {
        return style;
    }

    public void setStyle(Style style) {
        this.style = style;
    }

    public PictureCategory getCategory() {
        return category;
    }

    public void setCategory(PictureCategory category) {
        this.category = category;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public String getStyleCode() {
        return styleCode;
    }

    public void setStyleCode(String styleCode) {
        this.styleCode = styleCode;
    }

    public Integer getCloudFlag() {
        return cloudFlag;
    }

    public void setCloudFlag(Integer cloudFlag) {
        this.cloudFlag = cloudFlag;
    }

    public List<Material> getSubjectMaterialList() {
        return subjectMaterialList;
    }

    public void setSubjectMaterialList(List<Material> subjectMaterialList) {
        this.subjectMaterialList = subjectMaterialList;
    }
}
