package com.zxy.product.system.content;

/**
 * 内部开关-type枚举
 * code = t_internal_switch中的f_type
 */
public enum InternalSwitchEnum {


    BehaviorAcquisition(0, "行为数据采集"),
    RelatedCourses(1, "相关课程"),
    RelatedSubject(2, "相关专题"),
    IntelligentSearch(3, "智能搜索"),
    IntelligentRecommendation(4, "智能推荐"),
    GuessYouLike(5, "猜您喜欢"),
    TwentyThAnniversaryOfTheHomePage(6, "首页20周年活动"),
    IndividualCenter(7, "个人中心首页数据为兜底数据"),
    StudyCard(8, "学习卡片"),
    IntelligentCustomerService(9, "智能客服");


    private final int code;

    InternalSwitchEnum(int code, String desc) {
        this.code = code;
    }

    public int getCode() {
        return code;
    }
}
