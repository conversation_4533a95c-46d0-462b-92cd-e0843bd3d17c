package com.zxy.product.system.entity;

import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;
import java.util.Map;

@Document(collection="home_config_cache")
public class HomeCfgCacheItem<T> {

	public static final String COLL_NAME = "home_config_cache";

	public static final String M_NAV = "nav";

	public static final String M_NEWS = "news";

	public static final String M_ADVERTISEMENT = "advertisement";

	public static final String M_SKIN = "skin";

	public static final String M_FOOTER = "footer";

	public static final String M_CONTENT = "content";

	public static final String M_CONTENT_TREE = "content_tree";

	public static final String M_MODULE = "module";

	public static final String M_LECTURER = "lecturer";

	public static final String M_PERSON_PANEL = "person_panel";

	public static final String M_INTERACTIVE_COMMUNICATION = "interactive_communication";

	public static final String F_USER_ID = "userId";

	public static final String F_USER_TOKEN = "userToken";

	public static final String F_HOME_CFG_ID = "homeCfgId";

	public static final String F_MODULE = "module";

	public static final String F_MODULE_ID = "moduleId";

	public static final String F_DATA ="data";

	public static final String F_EXT_DATA = "extdata";

	public static final String F_LAST_UPDATE_TIME = "lastUpdateTime";

	public static final String F_OPT_ADD = "optAdd";

	public static final String F_OPT_EDIT = "optEdit";

	public static final String F_OPT_EDIT_EXT = "optEditExt";

	public static final String F_OPT_DEL = "optDel";

	public static final String F_OPT_SORT = "optSort";



	private String userId;

	private String userToken;

	private String module;

	private String homeCfgId;

	private String moduleId;

	private T data;

	private Map<String, Object> extData;

	private Date lastUpdateTime;

	private boolean optAdd;

	private boolean optEdit;


	private boolean optSort;

	private boolean optDel;


	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public String getUserToken() {
		return userToken;
	}

	public void setUserToken(String userToken) {
		this.userToken = userToken;
	}

	public String getHomeCfgId() {
		return homeCfgId;
	}

	public void setHomeCfgId(String homeCfgId) {
		this.homeCfgId = homeCfgId;
	}

	public String getModule() {
		return module;
	}

	public void setModule(String module) {
		this.module = module;
	}

	public String getModuleId() {
		return moduleId;
	}

	public void setModuleId(String moduleId) {
		this.moduleId = moduleId;
	}

	public T getData() {
		return data;
	}

	public void setData(T data) {
		this.data = data;
	}

	public Map<String, Object> getExtData() {
		return extData;
	}

	public void setExtData(Map<String, Object> extData) {
		this.extData = extData;
	}

	public Date getLastUpdateTime() {
		return lastUpdateTime;
	}

	public void setLastUpdateTime(Date lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}

	public boolean getOptAdd() {
		return optAdd;
	}

	public void setOptAdd(boolean optAdd) {
		this.optAdd = optAdd;
	}

	public boolean getOptEdit() {
		return optEdit;
	}

	public void setOptEdit(boolean optEdit) {
		this.optEdit = optEdit;
	}


	public boolean getOptSort() {
		return optSort;
	}

	public void setOptSort(boolean optSort) {
		this.optSort = optSort;
	}

	public boolean getOptDel() {
		return optDel;
	}

	public void setOptDel(boolean optDel) {
		this.optDel = optDel;
	}







}
