package com.zxy.product.system.entity;

import com.zxy.product.system.domain.vo.HomeParentVO;
import com.zxy.product.system.jooq.tables.pojos.HomeNewsEntity;

import java.io.Serializable;

/**
 * Created by <PERSON><PERSON><PERSON> on 17/2/22.
 */
public class HomeNews extends HomeNewsEntity {

    private static final long serialVersionUID = 5633916470640162587L;

    /**
     * 0 未发布 1 已发布
     */
    public static final int STATE_DRAFT = 0;
    public static final int STATE_ENABLE  = 1;

    /**
     * 0 未推广 1 已推广
     */
    public static final int PROMOTION_DRAFT  = 0;
    public static final int PROMOTION_ENABLE  = 1;

    private String categoryName;

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }


    public static class NewsChild extends HomeParentVO  implements Serializable {
        private static final long serialVersionUID = 5367146118652713594L;

        /**首页配置块：新闻资讯Id*/
        private String id;

        /**首页配置块：新闻资讯标题*/
        private String title;

        /**首页配置块：新闻资讯推广状态 0未推广 1已推广*/
        private Integer promotion;

        /**首页配置块：新闻资讯创建时间*/
        private Long createTime;

        /**首页配置块：新闻总结*/
        private String summary;

        /**首页配置块：新闻浏览量*/
        private Integer visitors;

        public String getId() { return id; }

        public void setId(String id) { this.id = id; }

        public String getTitle() { return title; }

        public void setTitle(String title) { this.title = title; }

        public Integer getPromotion() { return promotion; }

        public void setPromotion(Integer promotion) { this.promotion = promotion; }

        public Long getCreateTime() { return createTime; }

        public void setCreateTime(Long createTime) { this.createTime = createTime; }

        public String getSummary() { return summary; }

        public void setSummary(String summary) { this.summary = summary; }

        public Integer getVisitors() { return visitors; }

        public void setVisitors(Integer visitors) { this.visitors = visitors; }

        @Override
        public String toString() {
            return "NewsChild{" +
                    "id='" + id + '\'' +
                    ", title='" + title + '\'' +
                    ", promotion=" + promotion +
                    ", createTime=" + createTime +
                    ", summary='" + summary + '\'' +
                    ", visitors=" + visitors +
                    '}';
        }
    }


}
