package com.zxy.product.system.entity;

import com.zxy.product.system.jooq.tables.pojos.IntegralRuleEntity;

/**
 * Created by <PERSON> on 2016/12/23.
 */
public class IntegralRule extends IntegralRuleEntity{

    /**
     *
     */
    private static final long serialVersionUID = 982962621153413314L;
    private String aspectDesc;
    private Integer showRule;//1.只显示积分，2.积分在人次前，3积分在人次后
    private String descFirst;
    private String descSecond;
    private String descThird;

    public String getAspectDesc() {
        return aspectDesc;
    }

    public void setAspectDesc(String aspectDesc) {
        this.aspectDesc = aspectDesc;
    }

    public Integer getShowRule() {
        return showRule;
    }

    public void setShowRule(Integer showRule) {
        this.showRule = showRule;
    }

    public String getDescFirst() {
        return descFirst;
    }

    public void setDescFirst(String descFirst) {
        this.descFirst = descFirst;
    }

    public String getDescSecond() {
        return descSecond;
    }

    public void setDescSecond(String descSecond) {
        this.descSecond = descSecond;
    }

    public String getDescThird() {
        return descThird;
    }

    public void setDescThird(String descThird) {
        this.descThird = descThird;
    }
}
