package com.zxy.product.system.entity;

import com.zxy.product.system.jooq.tables.pojos.PointHistoryQuestEntity;

import java.io.Serializable;
import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @create 2024/1/18 15:40
 */
public class PointHistoryQuest extends PointHistoryQuestEntity implements Serializable {

    //历史累计任务,存在一次加多次分
    public static Set<String> pointTriggerByConditionSetTotal = new HashSet<String>(){
        {
            add("short_video_operate_a");
            add("short_video_operate_b");
            add("short_video_operate_c");
            add("short_video_operate_d");
        }
    };

    //历史累计任务,一次最多加一次分
    public static Set<String> pointTriggerByConditionSetOnce = new HashSet<String>(){
        {

            add("knowledge_download");
//            add("org_skill_certification_l1");
//            add("org_skill_certification_l2");
//            add("org_skill_certification_l3");

        }
    };

}
