/*
 * This file is generated by jOOQ.
 */
package com.zxy.product.system.jooq.tables;


import com.zxy.product.system.jooq.Keys;
import com.zxy.product.system.jooq.System;
import com.zxy.product.system.jooq.tables.records.HomeInteractiveCommunicationRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row6;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.TableImpl;


/**
 * 首页互动交流配置表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.12.4"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class HomeInteractiveCommunication extends TableImpl<HomeInteractiveCommunicationRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>system.t_home_interactive_communication</code>
     */
    public static final HomeInteractiveCommunication HOME_INTERACTIVE_COMMUNICATION = new HomeInteractiveCommunication();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<HomeInteractiveCommunicationRecord> getRecordType() {
        return HomeInteractiveCommunicationRecord.class;
    }

    /**
     * The column <code>system.t_home_interactive_communication.f_id</code>. 记录ID
     */
    public final TableField<HomeInteractiveCommunicationRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR(40).nullable(false), this, "记录ID");

    /**
     * The column <code>system.t_home_interactive_communication.f_module_config_id</code>. 配置模块ID
     */
    public final TableField<HomeInteractiveCommunicationRecord, String> MODULE_CONFIG_ID = createField("f_module_config_id", org.jooq.impl.SQLDataType.VARCHAR(40).defaultValue(DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "配置模块ID");

    /**
     * The column <code>system.t_home_interactive_communication.f_question_id</code>. 问吧记录id
     */
    public final TableField<HomeInteractiveCommunicationRecord, String> QUESTION_ID = createField("f_question_id", org.jooq.impl.SQLDataType.VARCHAR(40).defaultValue(DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "问吧记录id");

    /**
     * The column <code>system.t_home_interactive_communication.f_is_display</code>. 是否展示:0 不展示；1 展示
     */
    public final TableField<HomeInteractiveCommunicationRecord, Integer> IS_DISPLAY = createField("f_is_display", org.jooq.impl.SQLDataType.INTEGER.defaultValue(DSL.inline("0", org.jooq.impl.SQLDataType.INTEGER)), this, "是否展示:0 不展示；1 展示");

    /**
     * The column <code>system.t_home_interactive_communication.f_create_time</code>. 创建时间
     */
    public final TableField<HomeInteractiveCommunicationRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "创建时间");

    /**
     * Create a <code>system.t_home_interactive_communication</code> table reference
     */
    public HomeInteractiveCommunication() {
        this("t_home_interactive_communication", null);
    }

    /**
     * Create an aliased <code>system.t_home_interactive_communication</code> table reference
     */
    public HomeInteractiveCommunication(String alias) {
        this(alias, HOME_INTERACTIVE_COMMUNICATION);
    }

    private HomeInteractiveCommunication(String alias, Table<HomeInteractiveCommunicationRecord> aliased) {
        this(alias, aliased, null);
    }

    private HomeInteractiveCommunication(String alias, Table<HomeInteractiveCommunicationRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "首页互动交流配置表");
    }


    @Override
    public Schema getSchema() {
        return System.SYSTEM_SCHEMA;
    }

    @Override
    public UniqueKey<HomeInteractiveCommunicationRecord> getPrimaryKey() {
        return Keys.KEY_T_HOME_INTERACTIVE_COMMUNICATION_PRIMARY;
    }

    @Override
    public List<UniqueKey<HomeInteractiveCommunicationRecord>> getKeys() {
        return Arrays.<UniqueKey<HomeInteractiveCommunicationRecord>>asList(Keys.KEY_T_HOME_INTERACTIVE_COMMUNICATION_PRIMARY);
    }

    @Override
    public HomeInteractiveCommunication as(String alias) {
        return new HomeInteractiveCommunication(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public HomeInteractiveCommunication rename(String name) {
        return new HomeInteractiveCommunication(name, null);
    }
}
