package com.zxy.product.system.domain.vo.home.course.navigation;

import java.io.Serializable;
import java.util.List;

/**
 * 首页课程导航：复写App回显VO
 * <AUTHOR>
 * @date 2024年11月25日 11:22
 */
public class CourseNavigationAppVO extends CourseNavigationParentVO implements Serializable {
    private static final long serialVersionUID = 6806451783774521185L;

    /**首页配置快：分类课程|课程导航Id*/
    private String id;

    /**首页配置块：分类课程|课程导航数据Id*/
    private String dataId;

    /**首页配置块：分类课程|课程导航数据名称*/
    private String dataName;

    /**首页配置块：分类课程|课程导航URL链接地址*/
    private String url;

    /**首页配置块：分类课程|课程导航父节点Id*/
    private String parentId;

    /**首页配置块：分类课程|课程导航分类*/
    private Integer type;

    /**首页配置块：分类课程|课程导航是否显示 0隐藏 1显示*/
    private Integer show;

    /**首页配置块：分类课程|课程导航序号*/
    private Integer sort;

    /**首页配置块：分类课程|课程导航图片地址*/
    private String imgPath;

    /**首页配置快：分类课程|课程导航数据类型*/
    private Integer dataType;

    /**首页配置块：分类课程|课程导航子节点*/
    private List<CourseNavigationAppVO> children;

    public String getId() { return id; }

    public void setId(String id) { this.id = id; }

    public String getDataId() { return dataId; }

    public void setDataId(String dataId) { this.dataId = dataId; }

    public String getDataName() { return dataName; }

    public void setDataName(String dataName) { this.dataName = dataName; }

    public String getUrl() { return url; }

    public void setUrl(String url) { this.url = url; }

    public String getParentId() { return parentId; }

    public void setParentId(String parentId) { this.parentId = parentId; }

    public Integer getType() { return type; }

    public void setType(Integer type) { this.type = type; }

    public Integer getShow() { return show; }

    public void setShow(Integer show) { this.show = show; }

    public Integer getSort() { return sort; }

    public void setSort(Integer sort) { this.sort = sort; }

    public String getImgPath() { return imgPath; }

    public void setImgPath(String imgPath) { this.imgPath = imgPath; }

    public Integer getDataType() { return dataType; }

    public void setDataType(Integer dataType) { this.dataType = dataType; }

    public List<CourseNavigationAppVO> getChildren() { return children; }

    public void setChildren(List<CourseNavigationAppVO> children) { this.children = children; }

    @Override
    public String toString() {
        return "TreeChild{" +
                "id='" + id + '\'' +
                ", dataId='" + dataId + '\'' +
                ", dataName='" + dataName + '\'' +
                ", url='" + url + '\'' +
                ", parentId='" + parentId + '\'' +
                ", type=" + type +
                ", show=" + show +
                ", sort=" + sort +
                ", imgPath='" + imgPath + '\'' +
                ", dataType=" + dataType +
                ", children=" + children +
                '}';
    }
}
