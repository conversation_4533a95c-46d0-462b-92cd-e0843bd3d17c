package com.zxy.product.system.domain.vo.home.video;

import java.io.Serializable;

/**
 * 首页短视频：覆写App回显VO
 * <AUTHOR>
 * @date 2024年12月05日 14:04
 */
public class ShortVideoAppVO extends ShortVideoParentVO implements Serializable {
    private static final long serialVersionUID = -1005993602428940172L;

    /**短视频配置主键*/
    private String id;

    /**短视频网大上传封面*/
    private String coverPath;

    /**短视频咪咕默认封面路径*/
    private String coverOriginalPath;

    /**短视频标题*/
    private String shortVideoTitle;

    /**短视频Id*/
    private String shortVideoId;

    public String getId() { return id; }

    public void setId(String id) { this.id = id; }

    public String getCoverPath() { return coverPath; }

    public void setCoverPath(String coverPath) { this.coverPath = coverPath; }

    public String getCoverOriginalPath() { return coverOriginalPath; }

    public void setCoverOriginalPath(String coverOriginalPath) { this.coverOriginalPath = coverOriginalPath; }

    public String getShortVideoTitle() { return shortVideoTitle; }

    public void setShortVideoTitle(String shortVideoTitle) { this.shortVideoTitle = shortVideoTitle; }

    public String getShortVideoId() { return shortVideoId; }

    public void setShortVideoId(String shortVideoId) { this.shortVideoId = shortVideoId; }

    @Override
    public String toString() {
        return "ShortVideoCfgVO{" +
                "id='" + id + '\'' +
                ", coverPath='" + coverPath + '\'' +
                ", coverOriginalPath='" + coverOriginalPath + '\'' +
                ", shortVideoTitle='" + shortVideoTitle + '\'' +
                ", shortVideoId='" + shortVideoId + '\'' +
                '}';
    }
}
