package com.zxy.product.system.entity;

import com.zxy.common.base.exception.Code;

import java.util.ArrayList;
import java.util.List;

public class OrganizationImportEntity extends Organization {
    public static final int TEMPLATE_DATA_LIMIT = 5000;
    private static final long serialVersionUID = 1826846673308214945L;

    private String levelName;
    private String statusName;

    private int rowIndex;
    private boolean correct = true;
    private List<RowError> errors = new ArrayList<>();

    public boolean isCorrect() {
        return correct;
    }

    public void setCorrect(boolean correct) {
        this.correct = correct;
    }

    public int getRowIndex() {
        return rowIndex;
    }

    public void setRowIndex(int rowIndex) {
        this.rowIndex = rowIndex;
    }

    public List<RowError> getErrors() {
        return errors;
    }

    public void setErrors(List<RowError> errors) {
        this.errors = errors;
    }

    public void addError(Code code) {
        this.correct = false;
        if (!existError(code)) {
            this.errors.add(new RowError(rowIndex, code));
        }
    }
    public void addError(int column, Code code) {
        this.correct = false;
        this.errors.add(new RowError(rowIndex, column, code));
    }

    public String getLevelName() {
        return levelName;
    }

    public void setLevelName(String levelName) {
        this.levelName = levelName;
    }

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    public boolean existError(Code code) {
        return errors.stream().filter(e -> {
            if (e.getCode() != null) {
                return e.getCode().getCode() == code.getCode();
            }
            return false;
        }).count() > 0;
    }

    public class RowError {
        private int row = -1;
        private int column = -1;
        private Code code = null;

        public RowError(int row, Code code) {
            this.row = row;
            this.code = code;
        }
        public RowError(int row, int column, Code code) {
            this.row = row;
            this.column = column;
            this.code = code;
        }

        public Code getCode() {
            return code;
        }

        public int getColumn() {
            return column;
        }

        public int getRow() {
            return row;
        }

    }
}
