package com.zxy.product.system.entity;


import com.zxy.product.system.jooq.tables.pojos.PictureCategoryEntity;

/**
* @Description:    图片分类
* @Author:         liuc
* @CreateDate:     2021/6/23 11:27
* @UpdateRemark:   修改内容
* @Version:        1.0
*/
public class PictureCategory extends PictureCategoryEntity {
    private static final long serialVersionUID = 7892601663559818353L;

    /**
     * 创建人账号
     */
    private String createrCode;
    /**
     * 归属部门
     */
    private String organizationName;
    /**
     * 创建人姓名
     */
    private String creater;

    public String getCreater() {
        return creater;
    }

    public void setCreater(String creater) {
        this.creater = creater;
    }

    public String getCreaterCode() {
        return createrCode;
    }

    public void setCreaterCode(String createrCode) {
        this.createrCode = createrCode;
    }

    public String getOrganizationName() {
        return organizationName;
    }

    public void setOrganizationName(String organizationName) {
        this.organizationName = organizationName;
    }
}
