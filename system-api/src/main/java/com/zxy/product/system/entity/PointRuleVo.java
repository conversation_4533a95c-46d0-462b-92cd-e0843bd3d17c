package com.zxy.product.system.entity;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2023/12/6 16:16
 */
public class PointRuleVo implements Serializable {

    private static final long serialVersionUID = 6666723923155286710L;

    /**
     * 类型中文名
     */
    private String aspectDesc;

    /**
     * 规则类型对象
     */
    private List<PointRule> pointRuleList;


    public String getAspectDesc() {
        return aspectDesc;
    }

    public void setAspectDesc(String aspectDesc) {
        this.aspectDesc = aspectDesc;
    }

    public List<PointRule> getPointRuleList() {
        return pointRuleList;
    }

    public void setPointRuleList(List<PointRule> pointRuleList) {
        this.pointRuleList = pointRuleList;
    }
}
