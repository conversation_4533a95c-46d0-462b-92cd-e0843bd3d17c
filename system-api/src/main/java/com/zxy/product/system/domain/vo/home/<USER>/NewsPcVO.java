package com.zxy.product.system.domain.vo.home.news;

import java.io.Serializable;

/**
 * 首页新闻资讯：覆写PC回显VO
 * <AUTHOR>
 * @date 2024年11月25日 11:11
 */
public class NewsPcVO extends NewsParentVO implements Serializable {
    private static final long serialVersionUID = -7682623405062229679L;

    /**新闻资讯：配置Id*/
    private String id;

    /**新闻资讯：0未推广 1已推广*/
    private Integer promotion;

    /**新闻资讯：标题*/
    private String title;

    public String getId() { return id; }

    public void setId(String id) { this.id = id; }

    public Integer getPromotion() { return promotion; }

    public void setPromotion(Integer promotion) { this.promotion = promotion; }

    public String getTitle() { return title; }

    public void setTitle(String title) { this.title = title; }

    @Override
    public String toString() {
        return "NewsPcVO{" +
                "id='" + id + '\'' +
                ", promotion=" + promotion +
                ", title='" + title + '\'' +
                '}';
    }
}
