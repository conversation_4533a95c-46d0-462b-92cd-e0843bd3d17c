package com.zxy.product.system.entity;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class CommentDto implements Serializable{

    private static final long serialVersionUID = -2030385882674507623L;
    /**
     * 评论
     */
    public static final Integer TYPE_COMMENT=1;
    /**
     * 回复
     */
    public static final Integer TYPE_REPLY=2;

    /**
     * 主键
     */
    private String id;
    /**
     * 评论内容
     */
    private String content;
    /**
     * 业务来源类型
     */
    private Integer businessType;
    /**
     * 业务来源名称
     */
    private String businessName;
    /**
     * 发布人信息
     */
    private Member member;
    /**
     * 发布人组织信息
     */
    private Organization organization;
    /**
     * 发布时间
     */
    private Long createTime;
    /**
     * 评论类型（1：评论 2：回复）
     */
    private Integer type;
    /**
     * 是否隐藏
     */
    private Integer hide;

    /**
     * 是否置顶
     */
    private Integer topStatus;

    /**
     * 是否设置精华
     */
    private Integer essenceStatus;

    /**
     * 单位
     */
    private Organization company;

    /**
     * 红船复核状态:0待复核 1未采纳
     */
    private Integer redBoatToReview;
    /**
     * 红船审核状态:0已通过 1已拒绝 2待审核 3手动通过
     */
    private Integer redBoatAuditStatus;
    /**
     * 红船审核状态获取时间
     */
    private Long redBoatCreateTime;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Integer getBusinessType() {
        return businessType;
    }

    public void setBusinessType(Integer businessType) {
        this.businessType = businessType;
    }

    public String getBusinessName() {
        return businessName;
    }

    public void setBusinessName(String businessName) {
        this.businessName = businessName;
    }

    public Member getMember() {
        return member;
    }

    public void setMember(Member member) {
        this.member = member;
    }

    public Organization getOrganization() {
        return organization;
    }

    public void setOrganization(Organization organization) {
        this.organization = organization;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getHide() {
        return hide;
    }

    public void setHide(Integer hide) {
        this.hide = hide;
    }

    public Integer getTopStatus() {
        return topStatus;
    }

    public void setTopStatus(Integer topStatus) {
        this.topStatus = topStatus;
    }

    public Integer getEssenceStatus() {
        return essenceStatus;
    }

    public void setEssenceStatus(Integer essenceStatus) {
        this.essenceStatus = essenceStatus;
    }

    public Organization getCompany() {
        return company;
    }

    public void setCompany(Organization company) {
        this.company = company;
    }

    public Integer getRedBoatToReview() {
        return redBoatToReview;
    }

    public void setRedBoatToReview(Integer redBoatToReview) {
        this.redBoatToReview = redBoatToReview;
    }

    public Integer getRedBoatAuditStatus() {
        return redBoatAuditStatus;
    }

    public void setRedBoatAuditStatus(Integer redBoatAuditStatus) {
        this.redBoatAuditStatus = redBoatAuditStatus;
    }

    public Long getRedBoatCreateTime() {
        return redBoatCreateTime;
    }

    public void setRedBoatCreateTime(Long redBoatCreateTime) {
        this.redBoatCreateTime = redBoatCreateTime;
    }
}
