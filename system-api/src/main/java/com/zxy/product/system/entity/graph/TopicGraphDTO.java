package com.zxy.product.system.entity.graph;

import java.io.Serializable;
import java.util.List;

/**
 * 知识图谱——标签基础数据DTO
 *
 * <AUTHOR>
 * @date 2023年10月25日 11:06
 */
public class TopicGraphDTO implements Serializable {
    private static final long serialVersionUID=1L;

    /**标签Id*/
    private String labelId;

    /**节点类型*/
    private String nodeType;

    /**标签名称*/
    private String topicName;

    /**标签描述*/
    private String topicIntroduction;

    /**同义词标签*/
    private String synonymLabels;

    /**状态*/
    private String status;

    /**标签性质*/
    private String topicGroup;

    /**标签节点关系集合*/
    private List<GraphRelationDTO> topicRelationCollect;

    public String getLabelId() { return labelId; }

    public void setLabelId(String labelId) { this.labelId = labelId; }

    public String getNodeType() { return nodeType; }

    public void setNodeType(String nodeType) { this.nodeType = nodeType; }

    public String getTopicName() { return topicName; }

    public void setTopicName(String topicName) { this.topicName = topicName; }

    public String getTopicIntroduction() { return topicIntroduction; }

    public void setTopicIntroduction(String topicIntroduction) { this.topicIntroduction = topicIntroduction; }

    public String getSynonymLabels() { return synonymLabels; }

    public void setSynonymLabels(String synonymLabels) { this.synonymLabels = synonymLabels; }

    public List<GraphRelationDTO> getTopicRelationCollect() {
        return topicRelationCollect;
    }

    public void setTopicRelationCollect(List<GraphRelationDTO> topicRelationCollect) {
        this.topicRelationCollect = topicRelationCollect;
    }

    public String getTopicGroup() { return topicGroup; }

    public void setTopicGroup(String topicGroup) { this.topicGroup = topicGroup; }

    public String getStatus() { return status; }

    public void setStatus(String status) { this.status = status; }

    @Override
    public String toString() {
        return "TopicGraphDTO{" +
                "labelId='" + labelId + '\'' +
                ", nodeType='" + nodeType + '\'' +
                ", topicName='" + topicName + '\'' +
                ", topicIntroduction='" + topicIntroduction + '\'' +
                ", synonymLabels='" + synonymLabels + '\'' +
                ", status='" + status + '\'' +
                ", topicGroup='" + topicGroup + '\'' +
                ", topicRelationCollect=" + topicRelationCollect +
                '}';
    }
}
