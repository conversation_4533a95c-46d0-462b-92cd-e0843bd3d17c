package com.zxy.product.system.result;

import java.io.Serializable;

/**
 * 接口调用返回参数
 *
 */
public class ResultMsg implements Serializable{
    private static final long serialVersionUID = -1930985516283020796L;
    private String code;//返回状态：success,fail,error
	private String msg;//返回信息

	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public String getMsg() {
		return msg;
	}
	public void setMsg(String msg) {
		this.msg = msg;
	}

	public static ResultMsg getSuccessMsg(String msg){
		ResultMsg resultMsg = new ResultMsg();
		resultMsg.setCode("success");
		resultMsg.setMsg(msg);
		return resultMsg;
	}

	public static ResultMsg getFailMsg(String msg){
		ResultMsg resultMsg = new ResultMsg();
		resultMsg.setCode("fail");
		resultMsg.setMsg(msg);
		return resultMsg;
	}

	public static ResultMsg getErrorMsg(String msg){
		ResultMsg resultMsg = new ResultMsg();
		resultMsg.setCode("error");
		resultMsg.setMsg(msg);
		return resultMsg;
	}

	public static ResultMsg getResultMsg(String code,String msg){
		ResultMsg resultMsg = new ResultMsg();
		resultMsg.setCode(code);
		resultMsg.setMsg(msg);
		return resultMsg;
	}
}
