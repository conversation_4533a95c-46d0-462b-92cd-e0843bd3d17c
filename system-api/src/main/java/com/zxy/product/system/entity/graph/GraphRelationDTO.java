package com.zxy.product.system.entity.graph;

import java.io.Serializable;
import java.util.Objects;

/**
 * 知识图谱——基础数据节点关系
 *
 * <AUTHOR>
 * @date 2023年10月30日 9:28
 */
public class GraphRelationDTO implements Serializable {
    private static final long serialVersionUID=1L;

    /**关系类型*/
    private String type;
    /**头节点标签Id*/
    private String headId;
    /**尾节点标签Id集合*/
    private String tailId;

    public String getType() { return type; }

    public void setType(String type) { this.type = type; }

    public String getHeadId() { return headId; }

    public void setHeadId(String headId) { this.headId = headId; }

    public String getTailId() { return tailId; }

    public void setTailId(String tailId) { this.tailId = tailId; }

    @Override
    public String toString() {
        return "GraphRelationDTO{" +
                "type='" + type + '\'' +
                ", headId='" + headId + '\'' +
                ", tailId='" + tailId + '\'' +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof GraphRelationDTO)) return false;
        GraphRelationDTO that = (GraphRelationDTO) o;
        return Objects.equals(getType(), that.getType()) && Objects.equals(getHeadId(), that.getHeadId()) && Objects.equals(getTailId(), that.getTailId());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getType(), getHeadId(), getTailId());
    }
}
