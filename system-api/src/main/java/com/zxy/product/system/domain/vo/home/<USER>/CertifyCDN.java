package com.zxy.product.system.domain.vo.home.certify;

import com.zxy.product.system.domain.vo.HomeParentVO;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
 * CDN缓存的文件中的数据实体
 */
public class CertifyCDN extends HomeParentVO implements Serializable {

    private static final long serialVersionUID = -4643885426087425003L;
    private String id;
    private String name;
    private String hostDepartment;
    private String intro;
    private String imgPath;
    private String url;
    private Integer sort;
    private Integer dataType;
    private String dataExt;
    private String dataId;
    private String subheading;
    private String moduleConfigId;
    private List<ResourcesCDN> resources;
    private Integer courseCount;

    public static class ResourcesCDN implements Serializable {
        private static final long serialVersionUID = -8345417711022357075L;
        private String resourceId;
        private Integer resourceType;
        private Integer timeMinute;
        private Integer timeSecond;
        private String url;
        private String coverPath;
        private String name;

        public String getResourceId() {
            return resourceId;
        }

        public ResourcesCDN setResourceId(String resourceId) {
            this.resourceId = resourceId;
            return this;
        }

        public Integer getResourceType() {
            return resourceType;
        }

        public ResourcesCDN setResourceType(Integer resourceType) {
            this.resourceType = resourceType;
            return this;
        }

        public Integer getTimeMinute() {
            return timeMinute;
        }

        public ResourcesCDN setTimeMinute(Integer timeMinute) {
            this.timeMinute = timeMinute;
            return this;
        }

        public Integer getTimeSecond() {
            return timeSecond;
        }

        public ResourcesCDN setTimeSecond(Integer timeSecond) {
            this.timeSecond = timeSecond;
            return this;
        }

        public String getUrl() {
            return url;
        }

        public ResourcesCDN setUrl(String url) {
            this.url = url;
            return this;
        }

        public String getCoverPath() {
            return coverPath;
        }

        public ResourcesCDN setCoverPath(String coverPath) {
            this.coverPath = coverPath;
            return this;
        }

        public String getName() {
            return name;
        }

        public ResourcesCDN setName(String name) {
            this.name = name;
            return this;
        }

        @Override
        public final boolean equals(Object o) {
            if (!(o instanceof ResourcesCDN)) return false;

            ResourcesCDN that = (ResourcesCDN) o;
            return Objects.equals(getResourceId(), that.getResourceId()) && Objects.equals(getResourceType(), that.getResourceType()) && Objects.equals(getTimeMinute(), that.getTimeMinute()) && Objects.equals(getTimeSecond(), that.getTimeSecond()) && Objects.equals(getUrl(), that.getUrl()) && Objects.equals(getCoverPath(), that.getCoverPath()) && Objects.equals(getName(), that.getName());
        }

        @Override
        public int hashCode() {
            int result = Objects.hashCode(getResourceId());
            result = 31 * result + Objects.hashCode(getResourceType());
            result = 31 * result + Objects.hashCode(getTimeMinute());
            result = 31 * result + Objects.hashCode(getTimeSecond());
            result = 31 * result + Objects.hashCode(getUrl());
            result = 31 * result + Objects.hashCode(getCoverPath());
            result = 31 * result + Objects.hashCode(getName());
            return result;
        }
    }

    public Integer getCourseCount() {
        return courseCount;
    }

    public void setCourseCount(Integer courseCount) {
        this.courseCount = courseCount;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getHostDepartment() {
        return hostDepartment;
    }

    public void setHostDepartment(String hostDepartment) {
        this.hostDepartment = hostDepartment;
    }

    public String getIntro() {
        return intro;
    }

    public void setIntro(String intro) {
        this.intro = intro;
    }

    public String getImgPath() {
        return imgPath;
    }

    public void setImgPath(String imgPath) {
        this.imgPath = imgPath;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public Integer getDataType() {
        return dataType;
    }

    public void setDataType(Integer dataType) {
        this.dataType = dataType;
    }

    public String getDataExt() {
        return dataExt;
    }

    public void setDataExt(String dataExt) {
        this.dataExt = dataExt;
    }

    public String getDataId() {
        return dataId;
    }

    public void setDataId(String dataId) {
        this.dataId = dataId;
    }

    public String getSubheading() {
        return subheading;
    }

    public void setSubheading(String subheading) {
        this.subheading = subheading;
    }

    public String getModuleConfigId() {
        return moduleConfigId;
    }

    public void setModuleConfigId(String moduleConfigId) {
        this.moduleConfigId = moduleConfigId;
    }

    public List<ResourcesCDN> getResources() {
        return resources;
    }

    public CertifyCDN setResources(List<ResourcesCDN> resources) {
        this.resources = resources;
        return this;
    }

    @Override
    public final boolean equals(Object o) {
        if (!(o instanceof CertifyCDN)) return false;

        CertifyCDN that = (CertifyCDN) o;
        return Objects.equals(getId(), that.getId()) && Objects.equals(getName(), that.getName()) && Objects.equals(getHostDepartment(), that.getHostDepartment()) && Objects.equals(getIntro(), that.getIntro()) && Objects.equals(getImgPath(), that.getImgPath()) && Objects.equals(getUrl(), that.getUrl()) && Objects.equals(getSort(), that.getSort()) && Objects.equals(getDataType(), that.getDataType()) && Objects.equals(getDataExt(), that.getDataExt()) && Objects.equals(getDataId(), that.getDataId()) && Objects.equals(getSubheading(), that.getSubheading()) && Objects.equals(getModuleConfigId(), that.getModuleConfigId()) && Objects.equals(getResources(), that.getResources()) && Objects.equals(getCourseCount(), that.getCourseCount());
    }

    @Override
    public int hashCode() {
        int result = Objects.hashCode(getId());
        result = 31 * result + Objects.hashCode(getName());
        result = 31 * result + Objects.hashCode(getHostDepartment());
        result = 31 * result + Objects.hashCode(getIntro());
        result = 31 * result + Objects.hashCode(getImgPath());
        result = 31 * result + Objects.hashCode(getUrl());
        result = 31 * result + Objects.hashCode(getSort());
        result = 31 * result + Objects.hashCode(getDataType());
        result = 31 * result + Objects.hashCode(getDataExt());
        result = 31 * result + Objects.hashCode(getDataId());
        result = 31 * result + Objects.hashCode(getSubheading());
        result = 31 * result + Objects.hashCode(getModuleConfigId());
        result = 31 * result + Objects.hashCode(getResources());
        result = 31 * result + Objects.hashCode(getCourseCount());
        return result;
    }
}
