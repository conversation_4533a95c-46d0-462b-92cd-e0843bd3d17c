package com.zxy.product.system.entity;

import com.zxy.product.system.jooq.tables.pojos.PointRuleEntity;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2023/12/4 17:40
 */
public class PointRule extends PointRuleEntity implements Serializable {

    public final static String COURSE_REQUIRED_HOUR = "course_required_hour";//完{0}小时必修课程学习获得{1}积分，当天超过{2}次不再获得积分
    public final static String COURSE_OPTIONAL_HOUR = "course_optional_hour";//完成{0}小时选修课程学习获得{1}积分，当天超过{2}次不再获得积分
    public final static String WATCH_LIVE_HOUR = "watch_live_hour";//完成{0}小时直播学习获得{1}积分，当天超过{2}次不再获得积分
    public final static String WATCH_SHORT_VIDEO_MINUTE = "watch_short_video_minute";//完成{0}分钟短视频学习获得{1}积分，当天超过{2}次不再获得积分
    public final static String CLOUD_STUDY_COURSE = "cloud_study_course";//完成{0}分钟云书屋学习（含听书）获得{1}积分，当天超过{2}次不再获得积分
    public final static String COMPLETE_EXAM = "complete_exam";//通过{0}门考试获得{1}积分，当天超过{2}次不再获得积分
    public final static String PUBLISH_ASK_BAR = "publish_ask_bar";//上传知识/发布文章/发起提问{0}次获得{1}积分，当天超过{2}次不再获得积分
    public final static String UPLOAD_EXPERT = "upload_expert";//上传{0}个专家工作室课程（视频）/文章获得{1}积分,当天超过{2}次不再获得积分
    public final static String PUBLISH_SHORT_VIDEO = "publish_short_video";//发布短视频{0}次获得{1}积分,当天超过{2}次不再获得积分
    public final static String SHORT_VIDEO_OPERATE_A = "short_video_operate_a";//短视频被点赞，各类行为次数达到{0}次可获得{1}积分,最高获得{2}积分
    public final static String SHORT_VIDEO_OPERATE_B = "short_video_operate_b";//短视频被分享，各类行为次数达到{0}次可获得{1}积分,最高获得{2}积分
    public final static String SHORT_VIDEO_OPERATE_C = "short_video_operate_c";//短视频被收藏，各类行为次数达到{0}次可获得{1}积分,最高获得{2}积分
    public final static String SHORT_VIDEO_OPERATE_D = "short_video_operate_d";//短视频被关注，各类行为次数达到{0}次可获得{1}积分,最高获得{2}积分
    public final static String KNOWLEDGE_DOWNLOAD = "knowledge_download";//知识被下载累计达到{0}次可获得{1}积分,最高获得{2}次后不再获得积分
    public final static String SHARE = "share";//分享课程/知识/直播/文章{0}次可获得{1}积分,当天超过{2}次不再获得积分
    public final static String SELECTED_COMMENTS = "selected_comments";//发布/参与的评论被加精/置顶【评论区（课程、专题，知识）/问吧】{0}次可获得{1}积分,当天超过{2}次不再获得积分
    public final static String STUDIO_EXPERTS_DISCUSS = "studio_experts_discuss";//发布1个专家工作室讨论/回答{0}个问题可获得{1}积分,当天超过{2}次不再获得积分
    public final static String REPORT = "report";//被成功举报{0}次扣除{1}积分
    public final static String ORG_SKILL_CERTIFICATION_L1 = "org_skill_certification_l1";//完成{0}次集团公司统一组织的技能认证，初级认证等级获得{1}积分
    public final static String ORG_SKILL_CERTIFICATION_L2 = "org_skill_certification_l2";//完成{0}次集团公司统一组织的技能认证，中级认证等级获得{1}积分
    public final static String ORG_SKILL_CERTIFICATION_L3 = "org_skill_certification_l3";//完成{0}次集团公司统一组织的技能认证，高级认证等级获得{1}积分
    public final static String POINT_EXPIRE = "point_expire";//积分过期

    //由于短视频 点赞/分享/收藏/关注 需要分开统计,因此id为24时,需要把id为24,26,27,28的都进行修改
    public final static String[] SHORT_VIDEO_OPERATE_ARRAY = new String[]{"26", "27", "28"};

    public final static String SHORT_VIDEO_OPERATE_ID = "24";

    //参与活动的加分类型规则key
    //(活动使用 获取积分行为类型 1:发布痛点问题 2:审核短视频作品 3:发布短视频作品 4:短视频作品获得分享/点赞/收藏)
    public static Map<String,String> ACTIVIYT_RULE_KEY = new HashMap<String,String>(){
        {
            //4-短视频作品获得分享/点赞/收藏
            put(PointRule.SHORT_VIDEO_OPERATE_A,"4");
            put(PointRule.SHORT_VIDEO_OPERATE_B,"4");
            put(PointRule.SHORT_VIDEO_OPERATE_C,"4");
            //3-发布短视频作品
            put(PointRule.PUBLISH_SHORT_VIDEO,"3");
        }
    };

    private String descFirst;
    private String descSecond;
    private String descThird;
    private String descFourth;


    public String getDescFirst() {
        return descFirst;
    }

    public void setDescFirst(String descFirst) {
        this.descFirst = descFirst;
    }

    public String getDescSecond() {
        return descSecond;
    }

    public void setDescSecond(String descSecond) {
        this.descSecond = descSecond;
    }

    public String getDescThird() {
        return descThird;
    }

    public void setDescThird(String descThird) {
        this.descThird = descThird;
    }

    public String getDescFourth() {
        return descFourth;
    }

    public void setDescFourth(String descFourth) {
        this.descFourth = descFourth;
    }
}
