package com.zxy.product.system.entity;

import com.zxy.product.system.jooq.tables.pojos.VaultAuthAccountMappingEntity;
import java.io.Serializable;

/**
 * 金库账号与外部4A账号配置类
 */
public class VaultAuthAccountMapping extends VaultAuthAccountMappingEntity implements Serializable {

    /** 账号配置状态: 禁用 */
    public static final Integer VAULT_AUTH_ACCOUNT_STATE_DISABLE = 0;
    /** 账号配置状态: 启用 */
    public static final Integer VAULT_AUTH_ACCOUNT_STATE_ENABLE = 1;

    private Member member;

    public Member getMember() {
        return member;
    }

    public void setMember(Member member) {
        this.member = member;
    }
}
