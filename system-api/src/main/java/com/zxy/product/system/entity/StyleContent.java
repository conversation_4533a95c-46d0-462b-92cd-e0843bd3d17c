package com.zxy.product.system.entity;

import java.io.Serializable;

/**
* @Description:    样式内容详细
* @Author:         liuc
* @CreateDate:     2021/6/23 19:53
* @UpdateRemark:   修改内容
* @Version:        1.0
*/
public class StyleContent implements Serializable {
    private static final long serialVersionUID = 78510944878599049L;
    /**
     * 外层容器属性
     */
    private ParentContainer parentContainer;
    /**
     * 主标题属性
     */
    private MainTitleContainer mainTitleContainer;
    /**
     * 副标题属性
     */
    private SubTitleContainer subTitleContainer;

    public ParentContainer getParentContainer() {
        return parentContainer;
    }

    public void setParentContainer(ParentContainer parentContainer) {
        this.parentContainer = parentContainer;
    }

    public MainTitleContainer getMainTitleContainer() {
        return mainTitleContainer;
    }

    public void setMainTitleContainer(MainTitleContainer mainTitleContainer) {
        this.mainTitleContainer = mainTitleContainer;
    }

    public SubTitleContainer getSubTitleContainer() {
        return subTitleContainer;
    }

    public void setSubTitleContainer(SubTitleContainer subTitleContainer) {
        this.subTitleContainer = subTitleContainer;
    }
}
