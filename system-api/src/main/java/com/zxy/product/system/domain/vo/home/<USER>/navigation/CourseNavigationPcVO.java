package com.zxy.product.system.domain.vo.home.course.navigation;

import java.io.Serializable;
import java.util.List;

/**
 * 首页课程导航：复写PC回显VO
 * <AUTHOR>
 * @date 2024年11月25日 11:22
 */
public class CourseNavigationPcVO extends CourseNavigationParentVO implements Serializable {
    private static final long serialVersionUID = 6561919528039202811L;

    /**课程导航：数据名称*/
    private String dataName;

    /**课程导航：URL跳转地址*/
    private String url;

    private List<CourseNavigationPcVO> children;

    public String getDataName() { return dataName; }

    public void setDataName(String dataName) { this.dataName = dataName; }

    public String getUrl() { return url; }

    public void setUrl(String url) { this.url = url; }

    public List<CourseNavigationPcVO> getChildren() { return children; }

    public void setChildren(List<CourseNavigationPcVO> children) { this.children = children; }

    @Override
    public String toString() {
        return "CourseNavigationPcVO{" +
                "dataName='" + dataName + '\'' +
                ", url='" + url + '\'' +
                ", children=" + children +
                '}';
    }
}
