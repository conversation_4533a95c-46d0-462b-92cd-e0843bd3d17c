package com.zxy.product.system.entity;

import com.zxy.product.system.jooq.tables.pojos.VirtualSpaceGroupingEntity;

import java.util.List;

/**
 * <AUTHOR>
 */
public class VirtualSpaceGrouping extends VirtualSpaceGroupingEntity {


    private static final long serialVersionUID = 2850632367170030850L;
    public final static int STATUS_YES = 1;
    public final static int STATUS_no = 0;
    private String memberName;
    private Integer count;
    private List<VirtualSpaceGroupingDetails> virtualSpaceGroupingDetailsList;

    public List<VirtualSpaceGroupingDetails> getVirtualSpaceGroupingDetailsList() {
        return virtualSpaceGroupingDetailsList;
    }

    public void setVirtualSpaceGroupingDetailsList(List<VirtualSpaceGroupingDetails> virtualSpaceGroupingDetailsList) {
        this.virtualSpaceGroupingDetailsList = virtualSpaceGroupingDetailsList;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public String getMemberName() {
        return memberName;
    }

    public void setMemberName(String memberName) {
        this.memberName = memberName;
    }
}
