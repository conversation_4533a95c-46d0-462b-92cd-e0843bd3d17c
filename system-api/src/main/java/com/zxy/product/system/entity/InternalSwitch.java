package com.zxy.product.system.entity;

import com.zxy.product.system.jooq.tables.pojos.InternalSwitchEntity;

public class InternalSwitch extends InternalSwitchEntity {

    /**
     * 开关状态,0=关闭,1=开启
     */
    public final static int switchStatusOff = 0;
    /**
     * 开关状态,0=关闭,1=开启
     */
    public final static int switchStatusNO = 1;

    public final static String  KEY="internalSwitch-";


    /**
     * 0=行为数据采集,1=相关课程,2=相关专题,3=搜索,4=智能推荐,5=猜您喜欢,6=首页20周年,7=个人中心,8=学习卡片,9=智能客服
     */
    public final static int BehaviorAcquisition = 0;

    /**
     * 0=行为数据采集,1=相关课程,2=相关专题,3=搜索,4=智能推荐,5=猜您喜欢,6=首页20周年,7=个人中心,8=学习卡片,9=智能客服
     * ,10=热词,11=活动页直播,12=热门内容,13=发现页热议 ,14= 课程&专题列表
     */
    public final static int relatedCourses = 1;
    /**
     * 0=行为数据采集,1=相关课程,2=相关专题,3=搜索,4=智能推荐,5=猜您喜欢,6=首页20周年,7=个人中心,8=学习卡片,9=智能客服
     * ,10=热词,11=活动页直播,12=热门内容,13=发现页热议 ,14= 课程&专题列表
     */
    public final static int relatedSubject = 2;
    /**
     * 0=行为数据采集,1=相关课程,2=相关专题,3=搜索,4=智能推荐,5=猜您喜欢,6=首页20周年,7=个人中心,8=学习卡片,9=智能客服
     * ,10=热词,11=活动页直播,12=热门内容,13=发现页热议 ,14= 课程&专题列表
     */
    public final static int intelligentSearch = 3;
    /**
     * 0=行为数据采集,1=相关课程,2=相关专题,3=搜索,4=智能推荐,5=猜您喜欢,6=首页20周年,7=个人中心,8=学习卡片,9=智能客服
     * ,10=热词,11=活动页直播,12=热门内容,13=发现页热议 ,14= 课程&专题列表
     */
    public final static int intelligentRecommendation = 4;
    /**
     * 0=行为数据采集,1=相关课程,2=相关专题,3=搜索,4=智能推荐,5=猜您喜欢,6=首页20周年,7=个人中心,8=学习卡片,9=智能客服
     * ,10=热词,11=活动页直播,12=热门内容,13=发现页热议 ,14= 课程&专题列表
     */
    public final static int guessYouLike = 5;
    /**
     * 0=行为数据采集,1=相关课程,2=相关专题,3=搜索,4=智能推荐,5=猜您喜欢,6=首页20周年,7=个人中心,8=学习卡片,9=智能客服
     * ,10=热词,11=活动页直播,12=热门内容,13=发现页热议 ,14= 课程&专题列表
     */
    public final static int twentyThAnniversaryOfTheHomePage = 6;
    /**
     * 0=行为数据采集,1=相关课程,2=相关专题,3=搜索,4=智能推荐,5=猜您喜欢,6=首页20周年,7=个人中心,8=学习卡片,9=智能客服
     * ,10=热词,11=活动页直播,12=热门内容,13=发现页热议 ,14= 课程&专题列表
     */
    public final static int individualCenter = 7;
    /**
     * 0=行为数据采集,1=相关课程,2=相关专题,3=搜索,4=智能推荐,5=猜您喜欢,6=首页20周年,7=个人中心,8=学习卡片,9=智能客服
     * ,10=热词,11=活动页直播,12=热门内容,13=发现页热议 ,14= 课程&专题列表
     */
    public final static int studyCard = 8;
    /**
     * 0=行为数据采集,1=相关课程,2=相关专题,3=搜索,4=智能推荐,5=猜您喜欢,6=首页20周年,7=个人中心,8=学习卡片,9=智能客服
     * ,10=热词,11=活动页直播,12=热门内容,13=发现页热议 ,14= 课程&专题列表
     */
    public final static int intelligentCustomerService = 9;

    /**
     * 0=行为数据采集,1=相关课程,2=相关专题,3=搜索,4=智能推荐,5=猜您喜欢,6=首页20周年,7=个人中心,8=学习卡片,9=智能客服
     * ,10=热词,11=活动页直播,12=热门内容,13=发现页热议 ,14= 课程&专题列表
     */
    public final static int hotWord = 10;

    /**
     * 0=行为数据采集,1=相关课程,2=相关专题,3=搜索,4=智能推荐,5=猜您喜欢,6=首页20周年,7=个人中心,8=学习卡片,9=智能客服
     * ,10=热词,11=活动页直播,12=热门内容,13=发现页热议 ,14= 课程&专题列表
     */
    public final static int LivePage = 11;


    /**
     * 0=行为数据采集,1=相关课程,2=相关专题,3=搜索,4=智能推荐,5=猜您喜欢,6=首页20周年,7=个人中心,8=学习卡片,9=智能客服
     * ,10=热词,11=活动页直播,12=热门内容,13=发现页热议 ,14= 课程&专题列表
     */
    public final static int hotContent = 12;


    /**
     * 0=行为数据采集,1=相关课程,2=相关专题,3=搜索,4=智能推荐,5=猜您喜欢,6=首页20周年,7=个人中心,8=学习卡片,9=智能客服
     * ,10=热词,11=活动页直播,12=热门内容,13=发现页热议 ,14= 课程&专题列表     */
    public final static int DiscoveryPageIsHotTopic = 13;


    /**
     * 0=行为数据采集,1=相关课程,2=相关专题,3=搜索,4=智能推荐,5=猜您喜欢,6=首页20周年,7=个人中心,8=学习卡片,9=智能客服
     * ,10=热词,11=活动页直播,12=热门内容,13=发现页热议 ,14= 课程&专题列表
     */
    public final static int courseAndSubjectList = 14;


    /**
     * 0=行为数据采集,1=相关课程,2=相关专题,3=搜索,4=智能推荐,5=猜您喜欢,6=首页20周年,7=个人中心,8=学习卡片,9=智能客服
     * ,10=热词,11=活动页直播,12=热门内容,13=发现页热议 ,14= 课程&专题列表,15,数据回原
     */
    public final static int DATA_BACK_SOURCE = 15;

    /**
     * 0=行为数据采集,1=相关课程,2=相关专题,3=搜索,4=智能推荐,5=猜您喜欢,6=首页20周年,7=个人中心,8=学习卡片,9=智能客服
     * ,10=热词,11=活动页直播,12=热门内容,13=发现页热议 ,14= 课程&专题列表,15=数据回原,16=课程/专题列表切换
     */
    public final static int COURSE_AND_SUBJECT_SWITCH = 16;


    /**
     * 0=行为数据采集,1=相关课程,2=相关专题,3=搜索,4=智能推荐,5=猜您喜欢,6=首页20周年,7=个人中心,8=学习卡片,9=智能客服
     * ,10=热词,11=活动页直播,12=热门内容,13=发现页热议 ,14= 课程&专题列表,15=数据回原,16=课程/专题列表切换,17=全员发布限制
     */
    public final static int PUBLICATION_RESTRICTION = 17;

    /**
     * 个人画像,开关开启隐藏
     */
    public final static int PERSONAL_PORTRAIT = 18;


    /**
     * 配置学习计划按钮屏蔽
     */
    public final static int LEARNING_PLAN_PUSH = 25;

}
