package com.zxy.product.system.entity;

import java.io.Serializable;

/**
 * 积分每日任务Vo
 * <AUTHOR>
 * @create 2023/12/21 16:39
 */
public class PointDailyQuestVo implements Serializable {

    /**
     * 规则key
     */
    private String ruleSource;

    /**
     * 触发条件数值
     */
    private String ruleCount;

    /**
     * 每日最大触发次数
     */
    private String ruleMaxCount;

    /**
     * 每次加的分值
     */
    private String rulePoint;

    /**
     * 当日完成次数
     */
    private String finishCount;


    public String getRuleSource() {
        return ruleSource;
    }

    public void setRuleSource(String ruleSource) {
        this.ruleSource = ruleSource;
    }

    public String getRuleCount() {
        return ruleCount;
    }

    public void setRuleCount(String ruleCount) {
        this.ruleCount = ruleCount;
    }

    public String getRuleMaxCount() {
        return ruleMaxCount;
    }

    public void setRuleMaxCount(String ruleMaxCount) {
        this.ruleMaxCount = ruleMaxCount;
    }

    public String getRulePoint() {
        return rulePoint;
    }

    public void setRulePoint(String rulePoint) {
        this.rulePoint = rulePoint;
    }

    public String getFinishCount() {
        return finishCount;
    }

    public void setFinishCount(String finishCount) {
        this.finishCount = finishCount;
    }
}
