package com.zxy.product.system.content;

/**
 * <AUTHOR>
 *
 */
public class MessageHeaderContent {

    public static final String ID  = "id";
    public static final String NAME  = "name";
    public static final String MEMBER_ID  = "member_id";
    public static final String BUSINESS_ID  = "business_id";
    public static final String BUSINESS_TYPE  = "business_type";
    public static final String IDS = "ids";
    public static final String COUNT = "count";
    public static final String OPTER_TYPE = "type";
    public static final String OPTER_TYPE_ADD = "add";
    public static final String OPTER_TYPE_DELETE = "delete";
    public static final String OPTER_TYPE_UPDATE = "update";
    public static final String OPTER_TYPE_BATCH_INSERT = "batch_insert";
    public static final String PHONE_NUMBER = "phoneNumber";

    /**
     * 发送定时消息时，带上发送时间
     */
    public static final String SYSTEM_TIME = "system_time";


    /**
     * 发邮件
     */
    public static final String RECEIVERIDS = "receiverIds";
    public static final String SUBJECT = "subject";
    public static final String CONTENT = "content";
    public static final String TEXTCONTENT = "text_content";
    public static final String RECEIVER_EMAIL = "receiver_email";
    public static final String RECEIVER_NAME = "receiver_name";
    public static final String ORGANIZATION_ID = "organization_id";
    public static final String BUSINESS_CODE = "business_code";
    public static final String URL = "url";
    public static final String STYLE = "style";//邮件风格
    public static final String MSG_TYPE = "msg_type";//推送消息类型
    public static final String TEMPLATE_CODE = "template_code";//消息模板

    /**
     * pccw生成csv文件上传使用
     */
    public static final String YEAR_MONTH  = "yearMonth";
    public static final String YEAR  = "year";
    public static final String START_TIME  = "startTime";
    public static final String END_TIME  = "endTime";

    /**
     * 标签相关
     */
    public static final String TOPIC_ID = "topic_id";
    public static final String TOPIC_TYPE = "topic_type";

    /**
     * 言论审核
     */
    public static final String TYPE = "type";//审核类型
    public static final String OBJECT_ID = "object_id";//审核对象的id:如讨论id,回复id
    public static final String SOURCE_TYPE = "source_type";//来源
    public static final String SOURCE_ID = "source_id";//来源对象id
    //public static final String CONTENT = "content";//审核内容
    //public static final String MEMBER_ID = "member_id";//送审人
    public static final String BE_MEMBER_ID = "be_member_id";//被举报人
    public static final String ACCUSE_NUM = "accuse_num";//举报次数
   // public static final String ORGANIZATION_ID = "organization_id";//组织id
    public static final String SUBJECT_TITLE = "subject_title";//来源内容的标题，如问题标题，课程标题
    public static final String AUDIT_NOTE = "audit_note";//审核说明
    public static final String AUDIT_MEMBER_ID = "audit_member_Id";//审核人
    public static final String AUDIT_STATUS = "audit_status";//审核状态

    // 授权,菜单,角色
    public static final String CREATE_MEMBER_ID  = "create_member_id";
    public static final String MEMBER_IDS  = "member_ids";
    public static final String ADDED_MEMBER_IDS  = "added_member_ids";
    public static final String ORGANIZATION_IDS  = "organization_ids";
    public static final String INDETERMINATES = "indeterminates";
    public static final String CHILD_FINDS = "child_finds";
    public static final String OLD_MENU_URI = "old_menu_uri";
    public static final String NEW_MENU_URI = "new_menu_uri";
    public static final String GRANT_INFLUENCE_TYPE = "grant_influence_type";
    public static final String GRANT_INFLUENCE_MENU = "menu";
    public static final String GRANT_INFLUENCE_ROLE = "role";
    public static final String GRANT_INFLUENCE_ORG = "organizatioin";
    public static final String GRANT_INFLUENCE_MEMBER = "member";
    public static final String VALID_DATE = "validDate";

    // 扩展
    public static final String EXTENTION_VALUE = "extention-values";

    // 企业同步
    public static final String SYNC_STATUS = "sync_status";
    public static final String SYNC_MODULE = "sync_module";
    public static final String MODULE_SYNC_STATUS = "module_sync_status"; // 各模块同步状态
    public static final String COMPANY_NAME = "company_name";

    // 积分
    public static final String INTEGRAL_BUSINESS_ID = "integral_business_id";
    public static final String INTEGRAL_COUNT ="integral_count";
    public static final String INTEGRAL_MEMBER_ID = "integral_member_id";
    public static final String INTEGRAL_RULE_KEY = "integral_rule_key";
    public static final String INTEGRAL_DESCRIPTION = "integral_description";
    public static final String INTEGRAL_SCORE = "integral_score";
    public static final String INTEGRAL_CREATE_CLIENT = "INTEGRAL_CREATE_CLIENT";


    //消息模板
    public static final String MESSAGE_COURCE = "message_cource";//课程发模板消息
    public static final String MESSAGE_KNOWLEDGE = "message_knowledge";//知识发模板消息
    public static final String MESSAGE_SUBJECT = "message_subject";//专题发模板消息

	public static final String MESSAGE_TEMPLATE_ID = "templateId";
    public static final String MESSAGE_SENDER_ID = "senderId";
    public static final String HOST = "host";
    public static final String EMAIL_ADDRESS = "email_address"; // 邮箱地址
    public static final String SMS_ADDRESS = "sms_address"; // 短信目标号码
    public static final String CONTENT_PARAMS = "content_params"; // 内容参数
    public static final String TARGET_ID = "target_id"; // 自己的id
    public static final String SOURCE_TITLE = "source_title"; // 讨论所在来源的标题
    public static final String LEVEL = "level"; // 1回复了讨论 2回复了回复
	
	// mis同步
    public static final String VERSION = "version"; // mis同步版本
	public static final String SYNC_FAILE_OR_SUCCESS = "sync_faile_or_success"; // mis同步成功或者失败

    // ihr同步
    public static final String IHR_SYNC_STATUS = "ihr_sync_status"; // 同步状态
    public static final String IHR_FILE_NAME  = "ihr_file_name";    // ihr文件名
    public static final String IHR_SYNC_ITEM_SUCCESS_IDS = "ihr_sync_item_success_ids"; // ihr同步成功的ids
    public static final String IHR_SYNC_ITEM_FAILURE_IDS = "ihr_sync_item_failure_ids"; // ihr同步失败的ids
    public static final String IHR_SYNC_SUCCESS_IDS = "ihr_sync_success_ids"; // ihr同步成功的orgIds

    //行为数据采集
    public static final String BEHAVIOR_USERID = "behavior_userid"; // 用户id
    public static final String BEHAVIOR_CONTENT_ID  = "behavior_content_id";    // 内容id
    public static final String BEHAVIOR_CONTENT_TYPE  = "behavior_content_type"; // 内容类型
    public static final String BEHAVIOR_CONTENT_NAME = "behavior_content_name"; // 内容名称
    public static final String BEHAVIOR_CLIENT_TYPE = "behavior_client_type"; // 客户端类型
    public static final String BEHAVIOR_VALUE = "behavior_value"; // 类型：1:收藏；2：评论；3：评分；4：不喜欢/踩（暂时没有数据）; 5：赞；6：下载；7：分享
    public static final String BEHAVIOR_VALUE_EXTEND = "behavior_value_extend"; //  userBehavior 表的value 值
    public static final String BEHAVIOR_PAGE_SOURCE = "behavior_page_source"; // 具体值，如评论对应的value就是具体的评论信息，如果是评分，则对应具体的分值
    public static final String BEHAVIOR_STATUS = "behavior_status"; // 页面来源

    public static final String BEHAVIOR_START="behavior_start";//开始时间
    public static final String BEHAVIOR_END="behavior_end";//结束时间


    public static final String RULE_KEY = "ruleKey";
    public static final String TOTAL_NUMBER = "totalNumber";
    public static final String POINT_SOURCE_ID = "sourceId";
    public static final String ACTIVITY_FLAG = "activityFlag";//是否参与活动

    /**首页配置消息队列传递参数*/
    public static final String HOME_CFG_ID="homeCfgId";
    public static final String CLIENT_TYPE="clientType";

    /**
     * 外部访客-申请理由id
     */
    public static final String REASON_ID="reasonId";

}
