package com.zxy.product.system.entity;

import com.zxy.product.system.jooq.tables.pojos.CollectEntity;

public class Collect extends CollectEntity {

    // 业务类型:1课程 2专题 3考试 4班级 5直播 6调研 7知识
    public static final int BUSINESS_TYPE_COURSE = 1;
    public static final int BUSINESS_TYPE_SUBJECT = 2;
    public static final int BUSINESS_TYPE_EXAM = 3;
    public static final int BUSINESS_TYPE_CLASS = 4;
    public static final int BUSINESS_TYPE_LIVE = 5;
    public static final int BUSINESS_TYPE_RESEARCH = 6;
    public static final int BUSINESS_TYPE_KNOWLEDGE = 7;

    // 专家工作室新增类型 9文章 10讨论
    public static final int BUSINESS_TYPE_ARTICLE = 9;
    public static final int BUSINESS_TYPE_DISCUSS = 10;
    public static final Integer COLLECT_SOURCE_STUDIO = 1;

    public Integer loseEfficacy;// 收藏是否失效

    private static final long serialVersionUID = 5416807244237443489L;

    public Integer getLoseEfficacy() {
        return loseEfficacy;
    }

    public void setLoseEfficacy(Integer loseEfficacy) {
        this.loseEfficacy = loseEfficacy;
    }
}
