package com.zxy.product.system.domain.vo.home.big.banner;

import java.io.Serializable;

/**
 * 首页大BANNER：覆写App回显VO
 * <AUTHOR>
 * @date 2024年11月25日 11:32
 */
public class BigBannerAppVO extends BigBannerParentVO implements Serializable {
    private static final long serialVersionUID = -8376399682917875832L;

    /**大BANNER：配置Id*/
    private String id;

    /**大BANNER：业务Id*/
    private String businessId;

    /**大BANNER：业务类型*/
    private Integer businessType;

    /**大BANNER：标题*/
    private String title;

    /**大BANNER：App图片地址*/
    private String appImagePath;

    /**大BANNER：跳转地址*/
    private String linkAddress;

    /**大BANNER：跳转类型*/
    private Integer linkType;

    public String getId() { return id; }

    public void setId(String id) { this.id = id; }

    public String getBusinessId() { return businessId; }

    public void setBusinessId(String businessId) { this.businessId = businessId; }

    public Integer getBusinessType() { return businessType; }

    public void setBusinessType(Integer businessType) { this.businessType = businessType; }

    public String getTitle() { return title; }

    public void setTitle(String title) { this.title = title; }

    public String getAppImagePath() { return appImagePath; }

    public void setAppImagePath(String appImagePath) { this.appImagePath = appImagePath; }

    public String getLinkAddress() { return linkAddress; }

    public void setLinkAddress(String linkAddress) { this.linkAddress = linkAddress; }

    public Integer getLinkType() { return linkType; }

    public void setLinkType(Integer linkType) { this.linkType = linkType; }

    @Override
    public String toString() {
        return "BigBannerAppVO{" +
                "id='" + id + '\'' +
                ", businessId='" + businessId + '\'' +
                ", businessType=" + businessType +
                ", title='" + title + '\'' +
                ", appImagePath='" + appImagePath + '\'' +
                ", linkAddress='" + linkAddress + '\'' +
                ", linkType=" + linkType +
                '}';
    }
}
