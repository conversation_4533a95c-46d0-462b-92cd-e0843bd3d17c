package com.zxy.product.system.entity;

import com.zxy.product.system.jooq.tables.pojos.TopicRelationEntity;

/**
 * <AUTHOR> z<PERSON>yong
 * @ClassName : TopicRelation
 * @Description : 话题关联实体
 * @date : 2024-09-09 15:51
 */
public class TopicRelation extends TopicRelationEntity {

    private static final long serialVersionUID = 9084391460497918454L;

    //关联类型:1 同义词,2 关联词
    public static final int TYPE_SYNONYMS = 1;
    public static final int TYPE_RELATION = 2;

    public String name;//话题名称

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static TopicRelation builder(String topicId, String itemId, int type, int sequence) {
        TopicRelation relation = new TopicRelation();
        relation.forInsert();
        relation.setTopicId(topicId);
        relation.setItemId(itemId);
        relation.setType(type);
        relation.setSequence(sequence);
        return relation;
    }

}
