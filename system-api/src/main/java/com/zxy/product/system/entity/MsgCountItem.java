package com.zxy.product.system.entity;

import com.zxy.product.system.jooq.tables.pojos.MsgCountItemEntity;

/**
 * <AUTHOR>
 *
 */
public class MsgCountItem extends MsgCountItemEntity {

    /**
     *
     */
    private static final long serialVersionUID = 4519501965645725719L;

    public static final Integer TYPE_INNER = 1; // 站内消息
    public static final Integer TYPE_CLASS = 2; // 班级满意度评估
    public static final Integer TYPE_MARK_PAPER = 3; // 考试评审
    public static final Integer TYPE_MARK_COURSE = 4; // 课程作业评审
    public static final Integer TYPE_MARK_THEME = 5; // 专题作业评审
    public static final Integer TYPE_MARK_MOOK = 6; // 慕课作业审批
    public static final Integer TYPE_MARK_CLASS = 7; // 班级作业审批
    public static final Integer TYPE_AT_COURSE = 8; // 艾特我-评论区-课程
    public static final Integer TYPE_AT_SUBJECT = 9; // 艾特我-评论区-专题
    public static final Integer TYPE_AT_KNOWLEDGE = 10; // 艾特我-评论区-知识

    public static final Integer TYPE_APPROVE = 11; // 审批

    public static final Integer GROUP_INNER = 1; // 站内消息
    public static final Integer GROUP_TODO = 2; // 待办
    public static final Integer GROUP_AT = 3; // 艾特我

}
