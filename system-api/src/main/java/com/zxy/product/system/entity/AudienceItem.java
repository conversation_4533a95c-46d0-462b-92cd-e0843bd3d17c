package com.zxy.product.system.entity;

import com.zxy.product.system.jooq.tables.pojos.AudienceItemEntity;

/**
 * Created by keeley on 16/12/15.
 */
public class AudienceItem extends AudienceItemEntity{
    /**
     *
     */
    private static final long serialVersionUID = -5474788820844507382L;

    /**
     * 受众类型: 部门(不包含子部门)
     */
    public static final int TYPE_ORGANIZATION_ONLY_SELF = 1;
    /**
     * 受众类型: 部门(包含子部门)
     */
    public static final int TYPE_ORGANIZATION_ALL = 2;
    /**
     * 受众类型: 职务
     */
    public static final int TYPE_JOB = 3;
    /**
     * 受众类型: 职位
     */
    public static final int TYPE_POST = 4;
    /**
     * 受众类型: 人员
     */
    public static final int TYPE_MEMBER = 5;
    /**
     * 受众类型: 人员标签
     */
    public static final int TYPE_MEMBER_TAG = 6;

    @Override
    public int hashCode() {
        return (this.getId() + "," +
                this.getJoinId() + "," +
                this.getJoinType() + "," +
                this.getCreateTime() + "," +
                this.getReferenceCount()).hashCode();
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj != null && obj instanceof AudienceItem) {
            AudienceItem target = (AudienceItem) obj;
            return this.hashCode() == target.hashCode();
        }
        return false;
    }
}
