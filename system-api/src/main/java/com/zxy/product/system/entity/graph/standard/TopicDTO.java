package com.zxy.product.system.entity.graph.standard;

import java.io.Serializable;

/**
 * 知识图谱——标准标签(非根)DTO
 *
 * <AUTHOR>
 * @date 2023年11月09日 11:05
 */
public class TopicDTO implements Serializable {
    private static final long serialVersionUID=1L;

    /**标签(非根)Id*/
    private String topicId;

    /**标签(非根)名称*/
    private String topicName;

    /**标签(非根)描述*/
    private String topicDescription;

    /**状态*/
    private String status;

    /**标签性质*/
    private String group;

    public String getTopicId() { return topicId; }

    public void setTopicId(String topicId) { this.topicId = topicId; }

    public String getTopicName() { return topicName; }

    public void setTopicName(String topicName) { this.topicName = topicName; }

    public String getTopicDescription() { return topicDescription; }

    public void setTopicDescription(String topicDescription) { this.topicDescription = topicDescription; }

    public String getStatus() { return status; }

    public void setStatus(String status) { this.status = status; }

    public String getGroup() { return group; }

    public void setGroup(String group) { this.group = group; }

    @Override
    public String toString() {
        return "TopicDTO{" +
                "topicId='" + topicId + '\'' +
                ", topicName='" + topicName + '\'' +
                ", topicDescription='" + topicDescription + '\'' +
                ", status='" + status + '\'' +
                ", group='" + group + '\'' +
                '}';
    }
}
