package com.zxy.product.exam.async.task.szfn;

import com.zxy.product.exam.api.PaperInstanceService;
import com.zxy.product.exam.api.QuestionCopyService;
import com.zxy.product.exam.async.task.szfn.consumer.SzfnExamProfileConsumer;
import com.zxy.product.exam.async.task.szfn.producer.SzfnExamProfileProducer;
import com.zxy.product.exam.async.util.SzfnSftpUtil;
import com.zxy.product.exam.entity.ExamRecord;
import com.zxy.product.exam.entity.PaperInstance;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.EnvironmentAware;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Random;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.Calendar;

/**
 * 数智赋能考试档案定时任务
 */
@Component
@EnableAsync
public class SzfnExamProfileTask implements EnvironmentAware {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(SzfnExamProfileTask.class);

    private static final int PRODUCER_COUNT = 4;
    private static final int QUEUE_SIZE = 2000;

    private ApplicationContext applicationContext;
    private QuestionCopyService questionCopyService;
    private PaperInstanceService paperInstanceService;
    private SzfnSftpUtil szfnSftpUtil;

    private String szfnExamId;
    private String enableSynchronization;
    private String szfnExamPaperInstanceId;
    private String timeRangeConfig;

    @Override
    public void setEnvironment(Environment environment) {
        szfnExamId = environment.getProperty("szfn.exam.id", "");
        enableSynchronization = environment.getProperty("szfn.sync.enabled", "false");
        timeRangeConfig = environment.getProperty("szfn.sync.time.range", "");
    }

    @Autowired
    public void setApplicationContext(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }

    @Autowired
    public void setQuestionCopyService(QuestionCopyService questionCopyService) {
        this.questionCopyService = questionCopyService;
    }

    @Autowired
    public void setPaperInstanceService(PaperInstanceService paperInstanceService) {
        this.paperInstanceService = paperInstanceService;
    }

    @Autowired
    public void setSzfnSftpUtil(SzfnSftpUtil szfnSftpUtil) {
        this.szfnSftpUtil = szfnSftpUtil;
    }

    @Async
    @Scheduled(cron = "${szfn.sync.upload.cron:0 0 0,2,4,6,8,10,12,14,16,18,20,22 * * ?}")
    public void syncExamProfileData() {
        if (!Boolean.parseBoolean(enableSynchronization)) {
            LOGGER.info("数智赋能考试档案同步未启用");
            return;
        }
        
        // 获取时间范围：优先使用配置参数，否则调用getCurrentTimeRange方法
        long startTime;
        long endTime;

        if (timeRangeConfig != null && !timeRangeConfig.trim().isEmpty()) {
            // 解析配置参数：格式为 "startTime,endTime"
            long[] configTimeRange = parseTimeRangeConfig(timeRangeConfig);
            startTime = configTimeRange[0];
            endTime = configTimeRange[1];
            LOGGER.info("使用配置的时间范围：{} - {}", startTime, endTime);
        } else {
            // 使用getCurrentTimeRange方法获取时间范围
            long[] timeRange = getCurrentTimeRange();
            startTime = timeRange[0];
            endTime = timeRange[1];
            LOGGER.info("使用计算的时间范围：{} - {}", startTime, endTime);
        }
        
        uploadExamProfile(Optional.of(startTime), Optional.of(endTime));
    }
    
    public void uploadExamProfile(Optional<Long> startTime, Optional<Long> endTime) {
        String randomName = generateRandomString(5);
        
        try {
            // 1. 先查询所有考试人员ID
            List<PaperInstance> paperInstanceByExamId = paperInstanceService.getAllPaperInstanceByExamId(szfnExamId);
            if (CollectionUtils.isEmpty(paperInstanceByExamId)){
                LOGGER.info("没有找到试卷实例Id");
                return;
            }
            szfnExamPaperInstanceId = paperInstanceByExamId.get(0).getId();
            List<String> allMemberIds = queryExamMemberIds(startTime, endTime);
            if (allMemberIds.isEmpty()) {
                LOGGER.info("没有找到需要同步的考试人员");
                return;
            }
            
            LOGGER.info("查询到 {} 个考试人员需要同步", allMemberIds.size());
            
            // 2. 分批分配给多个生产者
            List<List<String>> memberIdBatches = partitionMemberIds(allMemberIds);
            
            // 3. 创建共享队列
            LinkedBlockingQueue<ExamRecord> profileQueue = new LinkedBlockingQueue<>(QUEUE_SIZE);
            
            // 4. 创建结束标识对象和生产者计数器
            ExamRecord endMarker = new ExamRecord();
            AtomicInteger activeProducers = new AtomicInteger(memberIdBatches.size());
            
            // 5. 创建多个生产者线程
            List<Thread> producerThreads = new ArrayList<>();
            for (int i = 0; i < memberIdBatches.size(); i++) {
                List<String> memberIds = memberIdBatches.get(i);
                
                // 从Spring容器获取生产者实例
                SzfnExamProfileProducer producer = applicationContext.getBean(SzfnExamProfileProducer.class);
                producer.init(profileQueue, endMarker, memberIds, activeProducers, szfnExamPaperInstanceId);
                
                Thread producerThread = new Thread(producer, 
                    "SzfnExamProfileProducerThread" + randomName + "-" + i);
                producerThreads.add(producerThread);
                producerThread.start();
            }
            
            // 6. 创建消费者线程
            SzfnExamProfileConsumer consumer = applicationContext.getBean(SzfnExamProfileConsumer.class);
            consumer.init(profileQueue, endMarker);
            
            Thread consumerThread = new Thread(consumer, 
                "SzfnExamProfileConsumerThread" + randomName);
            consumerThread.start();
            
            LOGGER.info("启动了 {} 个生产者线程和 1 个消费者线程", memberIdBatches.size());
            
            // 7. 等待所有线程完成
            try {
                long timeoutMinutes = 30;
                long timeoutMillis = timeoutMinutes * 60 * 1000;

                // 等待所有生产者完成
                boolean allProducersCompleted = waitForProducersCompletion(producerThreads, timeoutMillis);

                // 等待消费者完成
                boolean consumerCompleted = waitForConsumerCompletion(consumerThread, timeoutMillis);

                if (allProducersCompleted && consumerCompleted) {
                    LOGGER.info("所有线程正常完成");
                } else {
                    LOGGER.warn("部分线程超时未完成，已进行中断处理");
                }

            } catch (InterruptedException e) {
                LOGGER.error("等待线程完成时被中断，开始清理所有线程", e);
                // 中断所有线程
                interruptAllThreads(producerThreads, consumerThread);
                Thread.currentThread().interrupt();
            }
            
        } catch (Exception e) {
            LOGGER.error("数智赋能考试档案同步异常", e);
        }
    }
    
    /**
     * 查询考试人员ID列表
     */
    private List<String> queryExamMemberIds(Optional<Long> startTime, Optional<Long> endTime) {
        LOGGER.info("查询考试人员ID，时间范围：{} - {}", startTime.orElse(null), endTime.orElse(null));
        
        try {
            // 调用已实现的查询接口
            return questionCopyService.findNewEmployeeExam(startTime.orElse(0L), endTime.orElse(System.currentTimeMillis()), szfnExamId, szfnExamPaperInstanceId, 1, Integer.MAX_VALUE);
        } catch (Exception e) {
            LOGGER.error("查询考试人员ID失败", e);
            return new ArrayList<>();
        }
    }
    
    /**
     * 将人员ID列表分批
     */
    private List<List<String>> partitionMemberIds(List<String> allMemberIds) {
        List<List<String>> batches = new ArrayList<>();
        
        // 计算每个生产者处理的数量
        int batchSize = Math.max(1, allMemberIds.size() / PRODUCER_COUNT);
        if (allMemberIds.size() % PRODUCER_COUNT != 0) {
            batchSize++;
        }
        
        for (int i = 0; i < allMemberIds.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, allMemberIds.size());
            batches.add(allMemberIds.subList(i, endIndex));
        }
        
        LOGGER.info("将 {} 个人员ID分成 {} 批，每批约 {} 个", 
                   allMemberIds.size(), batches.size(), batchSize);
        
        return batches;
    }

    /**
     * 生成随机字符串
     */
    private String generateRandomString(int length) {
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        Random random = new Random();
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < length; i++) {
            sb.append(chars.charAt(random.nextInt(chars.length())));
        }
        return sb.toString();
    }

    /**
     * 等待所有生产者线程完成
     */
    private boolean waitForProducersCompletion(List<Thread> producerThreads, long timeoutMillis) throws InterruptedException {
        boolean allCompleted = true;

        for (Thread producerThread : producerThreads) {
            long startTime = System.currentTimeMillis();
            producerThread.join(timeoutMillis);

            if (producerThread.isAlive()) {
                LOGGER.warn("生产者线程 {} 超时未完成，开始中断", producerThread.getName());
                producerThread.interrupt();

                // 给线程一些时间来响应中断
                long remainingTime = Math.max(0, timeoutMillis - (System.currentTimeMillis() - startTime));
                if (remainingTime > 0) {
                    producerThread.join(Math.min(remainingTime, 5000)); // 最多等待5秒
                }

                if (producerThread.isAlive()) {
                    LOGGER.error("生产者线程 {} 中断后仍未停止", producerThread.getName());
                    allCompleted = false;
                } else {
                    LOGGER.info("生产者线程 {} 已响应中断并停止", producerThread.getName());
                }
            } else {
                LOGGER.info("生产者线程 {} 正常完成", producerThread.getName());
            }
        }

        LOGGER.info("所有生产者线程处理完成，成功率: {}/{}",
                   producerThreads.size() - (allCompleted ? 0 : 1), producerThreads.size());
        return allCompleted;
    }

    /**
     * 等待消费者线程完成
     */
    private boolean waitForConsumerCompletion(Thread consumerThread, long timeoutMillis) throws InterruptedException {
        long startTime = System.currentTimeMillis();
        consumerThread.join(timeoutMillis);

        if (consumerThread.isAlive()) {
            LOGGER.warn("消费者线程超时未完成，开始中断");
            consumerThread.interrupt();

            // 给线程一些时间来响应中断
            long remainingTime = Math.max(0, timeoutMillis - (System.currentTimeMillis() - startTime));
            if (remainingTime > 0) {
                consumerThread.join(Math.min(remainingTime, 10000)); // 最多等待10秒，因为消费者可能需要完成上传
            }

            if (consumerThread.isAlive()) {
                LOGGER.error("消费者线程中断后仍未停止");
                return false;
            } else {
                LOGGER.info("消费者线程已响应中断并停止");
                return true;
            }
        } else {
            LOGGER.info("消费者线程正常完成");
            return true;
        }
    }

    /**
     * 中断所有线程
     */
    private void interruptAllThreads(List<Thread> producerThreads, Thread consumerThread) {
        LOGGER.info("开始中断所有线程");

        // 中断所有生产者线程
        for (Thread producerThread : producerThreads) {
            if (producerThread.isAlive()) {
                LOGGER.info("中断生产者线程: {}", producerThread.getName());
                producerThread.interrupt();
            }
        }

        // 中断消费者线程
        if (consumerThread.isAlive()) {
            LOGGER.info("中断消费者线程");
            consumerThread.interrupt();
        }

        // 等待一段时间让线程响应中断
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        // 检查是否还有活跃线程
        int aliveCount = 0;
        for (Thread producerThread : producerThreads) {
            if (producerThread.isAlive()) {
                aliveCount++;
                LOGGER.warn("生产者线程 {} 仍然活跃", producerThread.getName());
            }
        }
        if (consumerThread.isAlive()) {
            aliveCount++;
            LOGGER.warn("消费者线程仍然活跃");
        }

        if (aliveCount > 0) {
            LOGGER.error("仍有 {} 个线程未能正常停止", aliveCount);
        } else {
            LOGGER.info("所有线程已成功停止");
        }
    }

    /**
     * 获取当前时间段的查询范围
     * 0点上传昨天22-0点的数据结果，2点上传0-2点的数据依次类推
     * @return [startTime, endTime]
     */
    private long[] getCurrentTimeRange() {
        Calendar now = Calendar.getInstance();
        int currentHour = now.get(Calendar.HOUR_OF_DAY);

        Calendar startCal = Calendar.getInstance();
        Calendar endCal = Calendar.getInstance();

        // 根据当前小时确定查询时间段
        if (currentHour == 0) {
            // 00:00 查询昨天 22:00-00:00 的数据
            startCal.add(Calendar.DAY_OF_MONTH, -1);
            startCal.set(Calendar.HOUR_OF_DAY, 22);
            endCal.set(Calendar.HOUR_OF_DAY, 0);
        } else {
            // 其他时间：查询前2小时到当前小时的数据
            // 2点查询0-2点，4点查询2-4点，6点查询4-6点，以此类推
            int startHour = currentHour - 2;
            if (startHour < 0) startHour = 0;
            startCal.set(Calendar.HOUR_OF_DAY, startHour);
            endCal.set(Calendar.HOUR_OF_DAY, currentHour);
        }

        // 设置分钟和秒为0
        startCal.set(Calendar.MINUTE, 0);
        startCal.set(Calendar.SECOND, 0);
        startCal.set(Calendar.MILLISECOND, 0);

        endCal.set(Calendar.MINUTE, 0);
        endCal.set(Calendar.SECOND, 0);
        endCal.set(Calendar.MILLISECOND, 0);

        return new long[]{startCal.getTimeInMillis(), endCal.getTimeInMillis()};
    }

    /**
     * 定时任务：处理数智赋能数据
     * 在上传时间节点前30分钟执行，处理上一次的数据
     * 上传时间节点：03, 06, 09, 12, 15, 18, 21, 00
     * 执行时间：02:30, 05:30, 08:30, 11:30, 14:30, 17:30, 20:30, 23:30
     */
    @Async
    @Scheduled(cron = "${szfn.sync.download.cron:0 30 2,5,8,11,14,17,20,23 * * ?}")
    public void processDigitalIntelligenceData() {
        LOGGER.info("开始执行数智赋能数据处理定时任务");

        try {
            boolean result = szfnSftpUtil.downloadAndSaveDigitalIntelligenceData();
            if (result) {
                LOGGER.info("数智赋能数据处理定时任务执行成功");
            } else {
                LOGGER.warn("数智赋能数据处理定时任务执行失败");
            }
        } catch (Exception e) {
            LOGGER.error("数智赋能数据处理定时任务执行异常", e);
        }
    }

    /**
     * 解析时间范围配置参数
     * @param timeRangeConfig 配置参数，格式为 "startTime,endTime"
     * @return [startTime, endTime]
     */
    private long[] parseTimeRangeConfig(String timeRangeConfig) {
        try {
            String[] parts = timeRangeConfig.split(",");
            if (parts.length != 2) {
                throw new IllegalArgumentException("时间范围配置格式错误，应为 'startTime,endTime'");
            }

            long startTime = Long.parseLong(parts[0].trim());
            long endTime = Long.parseLong(parts[1].trim());

            if (startTime >= endTime) {
                throw new IllegalArgumentException("开始时间必须小于结束时间");
            }

            LOGGER.info("解析时间范围配置成功：startTime={}, endTime={}", startTime, endTime);
            return new long[]{startTime, endTime};

        } catch (Exception e) {
            LOGGER.error("解析时间范围配置失败：{}，将使用默认的getCurrentTimeRange方法", timeRangeConfig, e);
            // 解析失败时返回getCurrentTimeRange的结果
            return getCurrentTimeRange();
        }
    }
}
