package com.zxy.product.system.entity;

import java.util.List;

import com.zxy.product.system.jooq.tables.pojos.SensitiveLogEntity;

/**
 * 敏感词库操作记录表
 * <AUTHOR>
 *
 */
public class SensitiveLog extends SensitiveLogEntity{
    private static final long serialVersionUID = -6684981447734671359L;
    public static final String URI = "operation/sensitive";

    private String saveNote;//保存说明
    //操作人name
    private String memberName;
    List<String> duplicateWords;

    public List<String> getDuplicateWords() {
        return duplicateWords;
    }

    public void setDuplicateWords(List<String> duplicateWords) {
        this.duplicateWords = duplicateWords;
    }

    public String getMemberName() {
        return memberName;
    }

    public void setMemberName(String memberName) {
        this.memberName = memberName;
    }

    public String getSaveNote() {
        return saveNote;
    }

    public void setSaveNote(String saveNote) {
        this.saveNote = saveNote;
    }

}
