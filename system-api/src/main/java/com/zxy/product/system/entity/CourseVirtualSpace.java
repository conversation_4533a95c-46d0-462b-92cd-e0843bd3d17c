package com.zxy.product.system.entity;

import com.zxy.product.system.jooq.tables.pojos.CourseVirtualSpaceEntity;

/**
 * 虚拟空间资源池
 * <AUTHOR>
 */
public class CourseVirtualSpace extends CourseVirtualSpaceEntity {

    /**
     * 0=禁用,1=启用
     */
    public static final int STATUS_ENABLED = 1;

    /**
     * 0=禁用,1=启用
     */
    public static final int STATUS_FORBIDDEN = 0;

    /**
     * 0=系统匹配 ,1=追加上级分享的课程
     */
    public static final int TYPE_INSIDE = 0;
    /**
     * 0=系统匹配 ,1=追加上级分享的课程
     */
    public static final int TYPE_ADD_TO = 1;


    /**
     * 0=课程, 1=考试, 2=专题
     */
    public static final int BUSINESS_TYPE_COURSE = 0;
    /**
     * 0=课程, 1=考试, 2=专题
     */
    public static final int BUSINESS_TYPE_EXAM = 1;
    /**
     * 0=课程, 1=考试, 2=专题
     */
    public static final int BUSINESS_TYPE_SUBJECT = 2;


}
