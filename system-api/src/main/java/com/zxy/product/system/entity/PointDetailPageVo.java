package com.zxy.product.system.entity;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @create 2023/12/14 15:16
 */
public class PointDetailPageVo implements Serializable {

    /**
     * 主键
     */
    private String  id;

    /**
     * 规则名称
     */
    private String  aspectType;

    /**
     * 来源
     */
    private String ruleSource;

    /**
     * 积分变化
     */
    private Integer pointChange;

    /**
     * 可用积分
     */
    private Integer pointBalance;

    /**
     * 累计积分
     */
    private Integer pointTotal;

    /**
     * 触发时间
     */
    private Long createTime;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getAspectType() {
        return aspectType;
    }

    public void setAspectType(String aspectType) {
        this.aspectType = aspectType;
    }

    public String getRuleSource() {
        return ruleSource;
    }

    public void setRuleSource(String ruleSource) {
        this.ruleSource = ruleSource;
    }

    public Integer getPointChange() {
        return pointChange;
    }

    public void setPointChange(Integer pointChange) {
        this.pointChange = pointChange;
    }

    public Integer getPointBalance() {
        return pointBalance;
    }

    public void setPointBalance(Integer pointBalance) {
        this.pointBalance = pointBalance;
    }

    public Integer getPointTotal() {
        return pointTotal;
    }

    public void setPointTotal(Integer pointTotal) {
        this.pointTotal = pointTotal;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }
}
