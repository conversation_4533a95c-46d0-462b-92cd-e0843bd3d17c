package com.zxy.product.system.domain.vo.home.featured.content;

import com.zxy.product.system.domain.vo.HomeParentVO;

import java.io.Serializable;

/**
 * 首页精选内容：覆写基类VO
 * <AUTHOR>
 * @date 2024年11月25日 15:46
 */
public class FeaturedContentParentVO extends HomeParentVO implements Serializable {
    private static final long serialVersionUID = 851917445268155438L;

    /**精选内容：非持久化数据类型（接口内部扭转）*/
    private Integer nonPersistentDataType;

    public Integer getNonPersistentDataType() { return nonPersistentDataType; }

    public void setNonPersistentDataType(Integer nonPersistentDataType) { this.nonPersistentDataType = nonPersistentDataType; }

    @Override
    public String toString() {
        return "FeaturedContentParentVO{" +
                "nonPersistentDataType=" + nonPersistentDataType +
                '}';
    }
}
