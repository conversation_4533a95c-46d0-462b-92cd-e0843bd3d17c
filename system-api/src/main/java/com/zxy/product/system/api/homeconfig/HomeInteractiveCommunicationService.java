package com.zxy.product.system.api.homeconfig;

import com.zxy.common.base.annotation.RemoteService;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.product.system.entity.HomeInteractiveCommunication;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * 首页互动交流配置Service
 * <AUTHOR>
 * @date 2025年07月14日 14:50
 */
@RemoteService
public interface HomeInteractiveCommunicationService {

    /**
     * 从缓存分页查询首页互动交流配置（自动关联问吧数据并更新缓存）
     * @param userId 用户ID
     * @param userToken 用户Token
     * @param page 页码
     * @param pageSize 每页大小
     * @param moduleConfigId 模块配置ID
     * @return 分页结果
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    PagedResult<HomeInteractiveCommunication> findPageFromCache(String userId, String userToken, Integer page, Integer pageSize, String moduleConfigId);

    /**
     * 从缓存根据ID查询详情
     * @param userId 用户ID
     * @param userToken 用户Token
     * @param id 记录ID
     * @return 配置详情
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    HomeInteractiveCommunication getFromCache(String userId, String userToken, String id);

    /**
     * 新增首页互动交流配置到缓存
     * @param userId 用户ID
     * @param userToken 用户Token
     * @param moduleConfigId 模块配置ID
     * @param questionId 问吧记录ID
     * @param isDisplay 是否显示
     * @return 新增的配置
     */
    @Transactional
    HomeInteractiveCommunication insertToCache(String userId, String userToken, String moduleConfigId, String questionId, Integer isDisplay);

    /**
     * 批量新增首页互动交流配置到缓存
     * @param userId 用户ID
     * @param userToken 用户Token
     * @param moduleConfigId 模块配置ID
     * @param questionIds 问吧记录ID列表
     * @param isDisplay 是否显示
     * @return 配置列表
     */
    @Transactional
    List<HomeInteractiveCommunication> batchInsertToCache(String userId, String userToken, String moduleConfigId, List<String> questionIds, Integer isDisplay);

    /**
     * 更新首页互动交流配置到缓存
     * @param userId 用户ID
     * @param userToken 用户Token
     * @param id 记录ID
     * @param moduleConfigId 模块配置ID
     * @param isDisplay 是否显示
     * @return 更新结果
     */
    @Transactional
    Map<String, Object> updateToCache(String userId, String userToken, String id, String moduleConfigId, Integer isDisplay);

    /**
     * 从缓存删除配置
     * @param userId 用户ID
     * @param userToken 用户Token
     * @param id 记录ID
     * @return 删除的记录ID
     */
    @Transactional
    String deleteFromCache(String userId, String userToken, String id);

    /**
     * 从缓存检查互动交流数据的显示状态
     * @param userId 用户ID
     * @param userToken 用户Token
     * @param ids 互动交流ID字符串，逗号分隔
     * @return 检查结果列表
     */
    @Transactional
    List<Map<String, Object>> checkDisplayStatusFromCache(String userId, String userToken, String ids);

    /**
     * 将缓存数据保存为最终数据
     * @param userId 用户ID
     * @param userToken 用户Token
     * @param homeConfigId 首页配置ID
     */
    @Transactional
    void saveAsFinal(String userId, String userToken, String homeConfigId);
}
